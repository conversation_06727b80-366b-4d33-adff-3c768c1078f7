# Lindenhof WordPress Theme

Een modern WordPress-thema voor groepsaccommodatie Lindenhof, gebouwd met Advanced Custom Fields (ACF) blocks en een modulaire architectuur.

## 📋 Overzicht

Dit thema is speciaal ontwikkeld voor de Lindenhof groepsaccommodatie website. Het bevat een complete set van custom blocks voor het presenteren van faciliteiten, boekingsinformatie, galerijen en contactgegevens.

### Belangrijkste Features

- **Custom ACF Blocks**: Modulaire content blocks voor flexibele pagina-opbouw
- **Huurkalender Integratie**: Directe koppeling met huurkalender.nl API
- **Responsive Design**: Mobile-first benadering met semantic HTML
- **Performance Optimized**: Minimale dependencies, geoptimaliseerde assets
- **Accessibility**: WCAG 2.1 compliant markup en navigatie
- **Modern JavaScript**: jQuery, GSAP animaties, Swup page transitions

## 🚀 Installatie

### Vereisten

- WordPress 5.8+
- PHP 7.4+
- Advanced Custom Fields Pro plugin
- Contact Form 7 (optioneel, voor contactformulieren)

### Setup Stappen

1. **Theme Installatie**
   ```bash
   # Upload theme naar wp-content/themes/
   # Of via WordPress admin: Appearance > Themes > Add New
   ```

2. **Plugin Installatie**
   - Installeer en activeer Advanced Custom Fields Pro
   - Installeer Contact Form 7 (indien contactformulieren gewenst)

3. **Theme Activatie**
   - Ga naar Appearance > Themes
   - Activeer "Lindenhof" theme

4. **Configuratie**
   - Ga naar Instellingen > Lindenhof
   - Voer huurkalender.nl API key in
   - Configureer contact informatie en social media links

## 🎛️ Admin Configuratie

### Lindenhof Instellingen

Ga naar **Instellingen > Lindenhof** voor:

#### API Instellingen
- **Huurkalender.nl API Key**: Vereist voor boekingswidget functionaliteit

#### Contact Informatie
- **E-mailadres**: Wordt getoond in contact block
- **Telefoonnummer**: Klikbare telefoonlink
- **Adres**: Volledig adres voor contact sectie

#### Social Media
- **Facebook URL**: Link naar Facebook pagina
- **Instagram URL**: Link naar Instagram account  
- **YouTube URL**: Link naar YouTube kanaal

## 🧩 Beschikbare Blocks

### Intro Block
Hoofdsectie met titel, ondertitel en content.
- **Velden**: Titel, Ondertitel, Inhoud, Achtergrond afbeelding
- **Gebruik**: Homepage hero sectie

### Faciliteiten Block
Overzicht van accommodatie faciliteiten.
- **Velden**: Titel, Faciliteiten (repeater met icoon, titel, beschrijving)
- **Gebruik**: Faciliteiten overzicht

### Algemeen Block
Algemene informatie sectie met tekst en afbeelding.
- **Velden**: Titel, Inhoud, Afbeelding, Afbeelding positie
- **Gebruik**: Over ons, algemene informatie

### Eten & Drinken Block
Informatie over eten, drinken en restaurants.
- **Velden**: Titel, Inhoud, Restaurants (repeater)
- **Gebruik**: Dining informatie, restaurant tips

### Omgeving Block
Activiteiten en bezienswaardigheden in de omgeving.
- **Velden**: Titel, Inhoud, Activiteiten (repeater)
- **Gebruik**: Lokale attracties, activiteiten

### Huurkalender Block
Boekingswidget met beschikbaarheidskalender.
- **Velden**: Titel, Ondertitel, Kalender tonen, Boekingsformulier tonen
- **Gebruik**: Boekingssectie
- **API**: Integreert met huurkalender.nl

### Images Marquee Block
Scrollende galerij van afbeeldingen.
- **Velden**: Titel, Afbeeldingen (gallery), Snelheid, Richting
- **Gebruik**: Foto galerij, sfeerbeelden

### Contact Block
Contact informatie, formulier en kaart.
- **Velden**: Titel, Inhoud, Formulier tonen, Kaart tonen
- **Gebruik**: Contact pagina sectie

## 🎨 Styling & Development

### CSS/LESS Structuur

```
assets/less/
├── style.less          # Main stylesheet (imports only)
└── constants.less      # Theme variables en constants
```

**Belangrijke constants:**
```less
@primaryColor: #2E5A3E;        // Forest green
@secondaryColor: #8B4513;      // Warm brown
@fontPrimary: 'Inter';         // Primary font
@fontSecondary: 'Playfair Display'; // Secondary font
```

### JavaScript Architectuur

```
assets/js/
└── lindenhof-blocks.js    # Main JavaScript file
```

**Hoofdcomponenten:**
- `Lindenhof.blocks`: Block initialization functions
- `Lindenhof.huurkalender`: Booking functionality
- `Lindenhof.marquee`: Image carousel logic
- `Lindenhof.swup`: Page transition handling

### BEM CSS Naming Convention

Alle blocks gebruiken BEM-achtige class naming:

```html
<!-- Block -->
<section class="intro-block">
  <!-- Element -->
  <h1 class="intro-block__title">
  <!-- Element with modifier -->
  <p class="intro-block__subtitle intro-block__subtitle--large">
</section>
```

## 🔧 Development Guidelines

### PHP Standards
- WordPress Coding Standards
- Proper escaping: `esc_html()`, `esc_url()`, `esc_attr()`
- Nonce verification voor AJAX calls
- Sanitization van user input

### JavaScript Standards  
- jQuery voor DOM manipulation
- GSAP voor animaties
- Swup voor page transitions
- Namespace: `window.Lindenhof`

### Security Best Practices
- Nonce verification voor alle AJAX requests
- Input sanitization en output escaping
- Capability checks voor admin functions
- Secure API communication

## 🌐 API Integratie

### Huurkalender.nl API

**Endpoints:**
- `POST /wp-json/lindenhof/v1/huurkalender/prefilter`
- `GET /wp-json/lindenhof/v1/huurkalender/availability`

**Authentication:**
- Bearer token via API key in theme settings
- WordPress nonce verification

**Usage:**
```javascript
// Booking form submission
Lindenhof.huurkalender.submitBookingForm($form);

// Calendar initialization  
Lindenhof.huurkalender.initCalendar($calendar);
```

## 📱 Responsive Design

### Breakpoints
- Mobile: < 768px
- Tablet: 768px - 1024px  
- Desktop: > 1024px

### Images
- Responsive images met `srcset`
- Lazy loading support
- WebP format support (via WordPress)

## 🚀 Performance

### Optimizations
- Minimale JavaScript dependencies
- CSS/JS concatenation en minification
- Image optimization
- Lazy loading voor afbeeldingen
- Efficient AJAX caching

### Loading Strategy
- Critical CSS inline
- Non-critical CSS deferred
- JavaScript loaded in footer
- Progressive enhancement

## 🐛 Troubleshooting

### Veelvoorkomende Problemen

**Huurkalender werkt niet:**
- Controleer API key in Instellingen > Lindenhof
- Verificeer internet connectie
- Check browser console voor JavaScript errors

**Blocks worden niet geladen:**
- Zorg dat ACF Pro plugin actief is
- Clear cache (indien caching plugin gebruikt)
- Controleer file permissions

**Animaties werken niet:**
- Verificeer dat GSAP correct geladen wordt
- Check browser compatibility
- Disable andere JavaScript plugins tijdelijk

### Debug Mode

Activeer debug mode in JavaScript:
```javascript
Lindenhof.config.debug = true;
```

## 📞 Support

Voor technische vragen of problemen:
- Check WordPress error logs
- Gebruik browser developer tools
- Controleer plugin conflicts

## 📄 Licentie

Dit thema is ontwikkeld specifiek voor Lindenhof groepsaccommodatie.

---

**Versie**: 1.0.0  
**Laatste update**: 2025-01-03  
**Ontwikkelaar**: Dennis Janssen
