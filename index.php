<?php get_header(); ?>
<!-- news overview -->

<?php if (have_posts()) : ?>
    <section class="newsOverviewBlock" data-init>
        <div class="contentWrapper">
            <div class="highlights">
                <div class="innerCol">
                <? for ($i = 0; $i < 1; $i++) : the_post(); ?>
                    <a href="<?= esc_url(get_permalink()) ?>" class="newsItem">
                        <div class="newsImage">
                            <img src="<?= esc_url(get_the_post_thumbnail_url(null, 'medium_large')) ?>" alt="<?= esc_attr(get_the_title()) ?>">
                        </div>
                        <div class="newsContent">
                            <div class="newsMeta">
                                <span class="textTitle primary smaller"><?= esc_html(get_the_date()) ?></span>
                            </div>
                            <h2 class="smallerTitle">
                                <?= esc_html(get_the_title()) ?>
                            </h2>
                        </div>
                    </a>
                <? endfor; ?>
                </div>
                <div class="innerCol">
                <? for ($i = 1; $i < 3; $i++) : the_post(); ?>
                    <a href="<?= esc_url(get_permalink()) ?>" class="newsItem">
                        <div class="newsImage">
                            <img src="<?= esc_url(get_the_post_thumbnail_url(null, 'medium_large')) ?>" alt="<?= esc_attr(get_the_title()) ?>">
                        </div>
                        <div class="newsContent">
                            <div class="newsMeta">
                                <span class="textTitle primary smaller"><?= esc_html(get_the_date()) ?></span>
                            </div>
                            <h2 class="smallerTitle">
                                <?= esc_html(get_the_title()) ?>
                            </h2>
                        </div>
                    </a>
                <? endfor; ?>
                </div>
            </div>
            <div class="allNews">
                <div class="newsGrid">
                    <?php while (have_posts()) : the_post(); ?>
                        <a href="<?= esc_url(get_permalink()) ?>" class="newsItem">
                            <div class="newsImage">
                                <img src="<?= esc_url(get_the_post_thumbnail_url(null, 'medium_large')) ?>" alt="<?= esc_attr(get_the_title()) ?>">
                            </div>
                            <div class="newsContent">
                                <div class="newsMeta">
                                    <span class="textTitle primary smaller"><?= esc_html(get_the_date()) ?></span>
                                </div>
                                <h2 class="smallerTitle">
                                    <?= esc_html(get_the_title()) ?>
                                </h2>
                            </div>
                        </a>
                    <?php endwhile; ?>
                </div>
            </div>
        </div>
    </section>
<?php endif; ?>
<?php get_footer(); ?>
