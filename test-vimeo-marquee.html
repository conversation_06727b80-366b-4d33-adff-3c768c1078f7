<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vimeo Marquee Block Test</title>
    <script src="libs/jquery.min.js"></script>
    <script src="libs/gsap.min.js"></script>
    <script src="libs/ScrollTrigger.min.js"></script>
    <script src="libs/hammer.min.js"></script>
    <script src="assets/js/parts/marquee.js"></script>
    <script src="blocks/js/gift-vimeo-marquee-block.js"></script>
    <style>
        /* Basic styling for testing */
        body { margin: 0; font-family: Arial, sans-serif; }
        .mediaMarqueeBlock { padding: 50px 0; }
        .contentWrapper { text-align: center; margin-bottom: 40px; }
        .marquee { white-space: nowrap; overflow: hidden; }
        .itemsContainer { display: inline-block; }
        .item { 
            display: inline-block; 
            position: relative; 
            width: 320px; 
            height: 180px; 
            margin: 0 10px; 
            cursor: pointer;
            border-radius: 10px;
            overflow: hidden;
        }
        .item video { 
            width: 100%; 
            height: 100%; 
            object-fit: cover; 
        }
        .playIcon {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 60px;
            height: 60px;
            background: rgba(0,0,0,0.7);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            pointer-events: none;
        }
        .playIcon svg { width: 30px; height: 30px; }
        
        .videoOverlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1000;
            display: none;
        }
        .background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            cursor: pointer;
        }
        .innerOverlay {
            position: absolute;
            width: 80vw;
            height: 45vw;
            max-width: 800px;
            max-height: 450px;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            background: #000;
            border-radius: 10px;
            overflow: hidden;
        }
        .placeVideo {
            width: 100%;
            height: 100%;
        }
        .placeVideo iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        .closeButton {
            position: absolute;
            top: -20px;
            right: -20px;
            width: 40px;
            height: 40px;
            background: white;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }
        .closeButton svg { width: 20px; height: 20px; }
    </style>
</head>
<body>
    <section class="mediaMarqueeBlock" data-init>
        <div class="contentWrapper">
            <h2>Vimeo Marquee Test</h2>
        </div>
        <div class="marqueeWrapper" data-init>
            <div class="marquee" data-marquee data-marquee-direction="right" data-marquee-speed="25" data-marquee-scroll-speed="5" data-marquee-swipe="true">
                <div class="marqueeScroll">
                    <div class="itemsContainer">
                        <!-- Test item 1 -->
                        <div class="item" data-video data-video-url="https://vimeo.com/76979871">
                            <video class="lazy" playsinline autoplay muted loop preload="none" 
                                   poster="https://i.vimeocdn.com/video/452001751-640x360.jpg"
                                   data-src="https://vimeo.com/76979871">
                                <source src="https://vimeo.com/76979871" type="video/mp4">
                            </video>
                            <div class="playIcon">
                                <svg width="68" height="48" viewBox="0 0 68 48">
                                    <path d="M 45,24 27,14 27,34" fill="#fff"></path>
                                </svg>
                            </div>
                        </div>
                        
                        <!-- Test item 2 -->
                        <div class="item" data-video data-video-url="https://vimeo.com/148751763">
                            <video class="lazy" playsinline autoplay muted loop preload="none" 
                                   poster="https://i.vimeocdn.com/video/548372490-640x360.jpg"
                                   data-src="https://vimeo.com/148751763">
                                <source src="https://vimeo.com/148751763" type="video/mp4">
                            </video>
                            <div class="playIcon">
                                <svg width="68" height="48" viewBox="0 0 68 48">
                                    <path d="M 45,24 27,14 27,34" fill="#fff"></path>
                                </svg>
                            </div>
                        </div>
                        
                        <!-- Test item 3 -->
                        <div class="item" data-video data-video-url="https://player.vimeo.com/video/90509568">
                            <video class="lazy" playsinline autoplay muted loop preload="none"
                                   poster="https://i.vimeocdn.com/video/481879190-640x360.jpg"
                                   data-src="https://player.vimeo.com/video/90509568">
                                <source src="https://player.vimeo.com/video/90509568" type="video/mp4">
                            </video>
                            <div class="playIcon">
                                <svg width="68" height="48" viewBox="0 0 68 48">
                                    <path d="M 45,24 27,14 27,34" fill="#fff"></path>
                                </svg>
                            </div>
                        </div>

                        <!-- Test item 4 - Progressive URL -->
                        <div class="item" data-video data-video-url="https://player.vimeo.com/progressive_redirect/playback/1116689393/rendition/720p/file.mp4?loc=external&signature=test">
                            <video class="lazy" playsinline autoplay muted loop preload="none"
                                   poster="https://i.vimeocdn.com/video/placeholder.jpg"
                                   data-src="https://player.vimeo.com/progressive_redirect/playback/1116689393/rendition/720p/file.mp4?loc=external&signature=test">
                                <source src="https://player.vimeo.com/progressive_redirect/playback/1116689393/rendition/720p/file.mp4?loc=external&signature=test" type="video/mp4">
                            </video>
                            <div class="playIcon">
                                <svg width="68" height="48" viewBox="0 0 68 48">
                                    <path d="M 45,24 27,14 27,34" fill="#fff"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <div class="videoOverlay" style="display: none;">
        <div class="background"></div>
        <div class="innerOverlay">
            <div class="closeButton">
                <svg viewBox="0 0 24 24">
                    <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41Z"/>
                </svg>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // Trigger the initialization
            $(document).trigger("initPage");
        });
    </script>
</body>
</html>
