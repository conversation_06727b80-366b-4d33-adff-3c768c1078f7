<?php get_header(); ?>

<?php
// Get the current post type
$post_type = get_queried_object();
$post_type_name = $post_type->name ?? '';

// Try to find a page with the same slug as the post type
$archive_page = get_page_by_path($post_type_name);

if ($archive_page) {
    // Set up the page as the current post so the_content() works
    global $post;
    $original_post = $post;
    $post = $archive_page;
    setup_postdata($post);

    // Display the page content
    the_content();

    // Restore original post
    $post = $original_post;
    wp_reset_postdata();
} else {
    // Fallback: show basic archive listing
    ?>
    <div class="archive-listing">
        <div class="contentWrapper">
            <h1><?= esc_html(get_the_archive_title()) ?></h1>

            <?php if (have_posts()): ?>
                <div class="posts-grid">
                    <?php while (have_posts()): the_post(); ?>
                        <article class="post-item">
                            <h2><a href="<?= esc_url(get_permalink()) ?>"><?= esc_html(get_the_title()) ?></a></h2>
                            <?php if (has_excerpt()): ?>
                                <div class="excerpt"><?= get_the_excerpt() ?></div>
                            <?php endif; ?>
                            <a href="<?= esc_url(get_permalink()) ?>" class="read-more">Lees meer →</a>
                        </article>
                    <?php endwhile; ?>
                </div>

                <?php
                // Pagination
                the_posts_pagination([
                    'prev_text' => '← Vorige',
                    'next_text' => 'Volgende →',
                ]);
                ?>
            <?php else: ?>
                <p>Er zijn geen items gevonden.</p>
            <?php endif; ?>
        </div>
    </div>
    <?php
}
?>

<?php get_footer(); ?>
