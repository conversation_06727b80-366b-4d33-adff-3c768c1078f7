<?php get_header(); ?>

<?php
// Get the Events page content (page with slug 'events')
$events_page = get_page_by_path('events');
if ($events_page) {
    // Set up the page as the current post so the_content() works
    global $post;
    $original_post = $post;
    $post = $events_page;
    setup_postdata($post);

    // Display the page content
    the_content();

    // Restore original post
    $post = $original_post;
    wp_reset_postdata();
} else {
    // Fallback: show a basic events listing if no Events page exists
    ?>
    <div class="events-archive">
        <div class="contentWrapper">
            <h1>Events</h1>
            <div class="eventsListingBlock">
                <div class="events-grid">
                    <?php
                    $events = get_posts([
                        'post_type' => 'event',
                        'posts_per_page' => -1,
                        'meta_key' => 'event_date',
                        'orderby' => 'meta_value',
                        'order' => 'ASC',
                        'meta_query' => [
                            [
                                'key' => 'event_date',
                                'value' => date('Y-m-d'),
                                'compare' => '>=',
                                'type' => 'DATE'
                            ]
                        ]
                    ]);

                    if ($events) {
                        foreach ($events as $event) {
                            $date = get_field('event_date', $event->ID);
                            $location = get_field('event_location', $event->ID);
                            $short_desc = get_field('event_short_description', $event->ID);
                            ?>
                            <div class="event-item">
                                <div class="event-content">
                                    <div class="event-meta">
                                        <span class="event-date"><?= esc_html($date) ?></span>
                                        <?php if ($location): ?>
                                            <span class="event-location"><?= esc_html($location) ?></span>
                                        <?php endif; ?>
                                    </div>
                                    <h2 class="event-title">
                                        <?= esc_html(get_the_title($event)) ?>
                                    </h2>
                                    <?php if ($short_desc): ?>
                                        <div class="event-description">
                                            <?= wpautop(esc_html($short_desc)) ?>
                                        </div>
                                    <?php endif; ?>
                                        Meer info →
                                    </a>
                                </div>
                            </div>
                            <?php
                        }
                    } else {
                        echo '<p>Er zijn momenteel geen aankomende events.</p>';
                    }
                    ?>
                </div>
            </div>
        </div>
    </div>
    <?php
}
?>

<?php get_footer(); ?>
