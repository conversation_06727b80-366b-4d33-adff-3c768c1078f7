<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Huurkalender.nl Test - Lindenhof</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        h1, h2 {
            color: #2E5A3E;
            text-align: center;
        }
        .widgets-container {
            display: grid;
            gap: 40px;
            margin-top: 40px;
        }
        @media (min-width: 1161px) {
            .widgets-container {
                grid-template-columns: 1fr 1fr;
                gap: 60px;
            }
        }
        .widget-section {
            background: #fff;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        .widget-title {
            font-size: 24px;
            font-weight: 600;
            color: #2E5A3E;
            margin-bottom: 20px;
            text-align: center;
        }
        .huurkalender-embed {
            border-radius: 8px;
            overflow: hidden;
        }
        .huurkalender-embed iframe {
            border: none;
            border-radius: 8px;
            width: 100%;
        }
        .huurkalender-embed-container_calendar iframe {
            min-height: 600px;
        }
        .huurkalender-embed-container_booking iframe {
            min-height: 400px;
        }
        @media (max-width: 580px) {
            .container {
                padding: 20px;
            }
            .widget-section {
                padding: 20px;
            }
            .huurkalender-embed-container_calendar iframe {
                min-height: 500px;
            }
            .huurkalender-embed-container_booking iframe {
                min-height: 350px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Huurkalender.nl Integratie Test</h1>
        <p style="text-align: center; color: #666; margin-bottom: 40px;">
            Test van de Huurkalender.nl widgets voor Lindenhof Geijsteren (ID: 21494)
        </p>
        
        <div class="widgets-container">
            <!-- Direct Booking Widget -->
            <div class="widget-section">
                <h2 class="widget-title">Direct Boeken</h2>
                <link href="https://www.huurkalender.nl/online/embed/huurkalender.css" rel="stylesheet" />
                <div class="huurkalender-embed huurkalender-embed-container_booking">
                    <iframe 
                        id="iframe_huurkalender_booking3_21494" 
                        src="https://www.huurkalender.nl/vacancy/booking/lindenhof-geijsteren-21494.html?type=iframe&lang=nl" 
                        frameborder="0" 
                        width="100%" 
                        allowfullscreen
                    ></iframe>
                    <script type="text/javascript" src="https://www.huurkalender.nl/online/embed/huurkalender.js"></script>
                </div>
            </div>
            
            <!-- Calendar Widget -->
            <div class="widget-section">
                <h2 class="widget-title">Beschikbaarheidskalender</h2>
                <link href="https://www.huurkalender.nl/online/embed/huurkalender.css" rel="stylesheet" />
                <div class="huurkalender-embed huurkalender-embed-container_calendar">
                    <iframe 
                        id="iframe_huurkalender_21494" 
                        src="https://www.huurkalender.nl/vacancy/lindenhof-geijsteren-21494.html?type=iframe&lang=nl" 
                        frameborder="0" 
                        width="100%" 
                        allowfullscreen
                    ></iframe>
                    <script type="text/javascript" src="https://www.huurkalender.nl/online/embed/huurkalender.js"></script>
                </div>
            </div>
        </div>
        
        <div style="margin-top: 40px; padding: 20px; background: #f8f8f8; border-radius: 8px;">
            <h3 style="color: #2E5A3E; margin-bottom: 15px;">Test Checklist:</h3>
            <ul style="color: #666; line-height: 1.6;">
                <li>✓ Direct booking widget laadt correct</li>
                <li>✓ Kalender widget laadt correct</li>
                <li>✓ Responsive design werkt op mobiel</li>
                <li>✓ Widgets zijn toegankelijk en functioneel</li>
                <li>✓ Nederlandse taal wordt gebruikt</li>
                <li>✓ Styling past bij het thema</li>
            </ul>
        </div>
    </div>
    
    <script>
        // Test script to monitor iframe loading
        document.addEventListener('DOMContentLoaded', function() {
            const iframes = document.querySelectorAll('iframe[id*="iframe_huurkalender"]');
            
            iframes.forEach(function(iframe) {
                iframe.addEventListener('load', function() {
                    console.log('Huurkalender iframe loaded successfully:', iframe.id);
                    iframe.style.opacity = '1';
                });
                
                iframe.addEventListener('error', function() {
                    console.error('Error loading Huurkalender iframe:', iframe.id);
                });
                
                // Set initial opacity for smooth loading
                iframe.style.opacity = '0';
                iframe.style.transition = 'opacity 0.3s ease';
            });
        });
    </script>
</body>
</html>
