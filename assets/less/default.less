// out: false
@import 'vw_values.less';
@import 'constants.less'; 

* {
  box-sizing: border-box;
  cursor:default;
  letter-spacing: 0;
  margin:0; 
  padding:0;
  position:relative;
  &::selection {
    background: @primaryColor;
    color: @hardWhite;
  }
  &::-webkit-selection {
    background: @primaryColor;
    color: @hardWhite;
  }
  &:focus {
    outline: none;
  }
}

 
html.lenis {
  height: auto;
}

.lenis.lenis-smooth {
  scroll-behavior: auto;
}

.lenis.lenis-smooth [data-lenis-prevent] {
  overscroll-behavior: contain;
}

.lenis.lenis-stopped {
  overflow: hidden;
}

.lenis.lenis-scrolling iframe {
  pointer-events: none;
}
html {
  overflow-x: hidden;
}
body {
  background: @hardWhite;
  color: @hardBlack;
  font-family: 'Liter', arial, sans-serif;
  overflow-x: hidden;
  width: 100vw;
  line-height: 1;
  font-size: @vw16;
  strong, em {
    font-weight: 300;
  }
  p {
    font-size: @vw16;
    color: @hardBlack;
    font-weight: 500;
    line-height: 1.4;
    a {
      cursor: pointer;
      text-decoration: none;
      font-weight: 700;
      .transition(.15s);
      &:hover {
        opacity: .6;
      }
    }
  }
  ul {
    line-height: 1.4;
  }
}

body.touch {
  a,
  .button {
    &:hover {
      opacity: 1 !important;
      color: inherit !important;
      background: inherit !important;
      transform: none !important;
      transition: none !important;
    }
  }
  .button {
    &:hover {
      background: @primaryColor !important;
    }
  }
}

[data-scroll-section] {
  background: @hardWhite;
  // padding: @vw100 + @vw30 + @vw5 0 0 0;
}

[data-scroll-container] {
  position: absolute;
  top: 0;
  width: 100%;
}

section {
  margin: @vw100 + @vw20 0;
  &.noMarginTop {
    margin-top: 0;
  }
  &.noMarginBottom {
    margin-bottom: 0;
  } 
  &:first-of-type {
    margin-top: 0;
  }
  &.primary {
    color: @hardWhite;
    background: @primaryColor;
    .contentWrapper {
      padding-top: @vw100;
      padding-bottom: @vw100;
    }
    .text {
      p {
        color: @hardWhite;
      }
    }
  }
  &.almostWhite {
    padding: 0 @vw100 + @vw30;
    color: @hardBlack;
    .contentWrapper {
      padding-top: @vw100;
      padding-bottom: @vw100;
      background: @almostWhite;
    }
  }
}

.contentWrapper {
  display: block;
  width: 100%;
  padding: 0 @vw100 + @vw40;
  &.small {
    padding: 0 @vw100 + (@vw112 * 3) + (@vw16 * 3);
  }
  &.smaller {
    padding: 0 @vw100 + @vw112 + @vw16;
  }
}

a {
  cursor: pointer;
  text-decoration: none;
} 

.textLink {
  color: @primaryColor;
  padding-bottom: @vw10;
  display: inline-block;
  position: relative;
  &:hover {
    &:before, &:after {
      .transitionMore(width, .3s, 0s, cubic-bezier(0.87, 0, 0.13, 1));
    }
    &:before {
      width: 0%;
      transition-delay: 0s;
    }
    &:after {
      width: 100%;
      transition-delay: .15s;
    }
  }
  
  &:before, &:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: auto;
    right: 0;
    background: @primaryColor;
    width: 100%;
    height: 1px;
  }
  &:after {
    left: 0;
    right: auto;
    width: 0%;
  }
}

.button {
  .rounded(@vw100);
  display: inline-block;
  transition: background .3s, color .3s, border-color .3s;
  cursor: pointer;
  color: @primaryColor;
  padding: @vw14 @vw26;
  position: relative;
  text-align: center;
  line-height: 1.15;
  border: 1px solid @primaryColor;
  &.primary {
    background: @primaryColor;
    color: @hardWhite;
    &:hover {
      background: @secondaryColor;
      border-color: @secondaryColor;
    }
  }
  &:hover {
    background: @primaryColor;
    color: @hardWhite;
    .absoluteText {
      opacity: 0;
      .transform(translate(-50%, -100%));
      transition-delay: 0s;
      &:nth-child(2) {
        transform: translate(-50%, -50%);
        opacity: 1;
        transition-delay: .15s;
      }
    }
  }
  span {
    cursor: pointer;
  }
  .innerText {
    visibility: hidden;
  }
  .absoluteText {
    position: absolute;
    width: 100%;
    height: auto;
    top: 50%;
    left: 50%;
    .transform(translate(-50%, -50%));
    transition: transform .3s, opacity .3s;
    transition-delay: .15s;
    &:nth-child(2) {
      opacity: 0;
      .transform(translate(-50%, 0%));
    }
  }
}

.sliderButton {
  position: absolute;
  cursor: pointer;
  width: @vw70;
  color: @hardWhite;
  height: @vw70;
  background: @primaryColor;
  text-align: center;
  overflow: hidden;
  line-height: @vw70;
  .rounded(50%);
  top: 50%;
  transition: color .3s 0s ease-in-out, opacity .3s;
  &:hover {
    opacity: .6;
  }
  &.next {
    right: 0;
    transform: translateY(-50%) translateX(50%);
  }
  &.prev {
    left: 0;
    transform: translateY(-50%) translateX(-50%);
  }
}

@media all and (max-width: 1160px) {
  body {
    font-size: @vw16-1160;
    p {
      font-size: @vw16-1160;
    }
  }

  section {
    margin: @vw100-1160 + @vw20-1160 0;
    &:first-of-type {
      padding-top: @vw100-1160 * 1.5;
    }
    &.white {
      padding: @vw100-1160 0;
    }
    &.primary {
      .contentWrapper {
        padding-top: @vw100-1160;
        padding-bottom: @vw100-1160;
      }
    }
    &.almostWhite {
      padding: 0 ;
      .contentWrapper {
        padding-top: @vw100-1160;
        padding-bottom: @vw100-1160;
      }
    }
  }

  .contentWrapper {
    padding: 0 @vw60-1160;
    &.small {
      padding: 0 (@vw112-1160 * 2) + (@vw16-1160 * 2);
    }
    &.smaller {
      padding: 0 @vw60-1160;
    }
  }

  .button {
    padding: @vw14-1160 @vw40-1160;
    border-radius: @vw40-1160;
    &.smaller {
      padding: @vw10-1160 @vw22-1160;
    }
  }

  .sliderButton {
    width: @vw70-1160;
    height: @vw70-1160;
    line-height: @vw70-1160;
  }
}

@media all and (max-width: 580px) {
  body {
    font-size: @vw24-580;
    p {
      font-size: @vw24-580;
    }
  }

  section {
    margin: @vw100-580 + @vw20-580 0;
    &:first-of-type {
      padding-top: @vw100-580 * 2;
    }
    &.white {
      padding: @vw100-580 0;
    }
    &.primary {
      .contentWrapper {
        padding-top: @vw100-580;
        padding-bottom: @vw100-580;
      }
    }
    &.almostWhite {
      padding: 0;
      .contentWrapper {
        padding-top: @vw100-580;
        padding-bottom: @vw100-580;
      }
    }
  }

  .contentWrapper {
    padding: 0 @vw30-580;
    &.small, &.smaller {
      padding: 0 @vw30-580;
    }
  }

  .button {
    padding: @vw22-580 @vw40-580;
    border-radius: @vw40-580;
    &.smaller {
      padding: @vw15-580 @vw22-580;
    }
  }

  .sliderButton {
    width: @vw70-580;
    height: @vw70-580;
    line-height: @vw70-580;
  }
}
