.hugeTitle.white,
.biggerTitle.white,
.bigTitle.white,
.subTitle.white,
.text.white,
.writtenTitle.white,
.title.white,
.textTitle.white {
  color: #FFFFFF;
}
.hugeTitle.secondary,
.biggerTitle.secondary,
.bigTitle.secondary,
.subTitle.secondary,
.text.secondary,
.writtenTitle.secondary,
.title.secondary,
.textTitle.secondary {
  color: #8B4513;
}
.hugeTitle {
  font-size: 8.102vw;
  font-family: "Playfair Display", Arial;
  font-weight: 400;
}
.biggerTitle {
  font-size: 4.514vw;
  font-family: "Gotham", Arial;
  font-weight: 1000;
  line-height: 0.8;
  text-transform: uppercase;
  letter-spacing: -4px;
}
.biggerTitle strong {
  letter-spacing: -12px;
  font-weight: 1000;
  font-size: 11.574vw;
}
.bigTitle {
  font-size: 2.431vw;
  font-family: "Playfair Display", Aria<PERSON>;
  font-weight: 400;
  line-height: 1.2;
}
.title {
  font-size: 2.083vw;
  font-family: "Playfair Display", Arial;
  font-style: italic;
  font-weight: 300;
}
.title.bold {
  font-weight: 700;
}
.smallTitle {
  font-size: 1.62vw;
  font-family: "Liter", Arial;
  font-weight: 300;
  line-height: 1.2;
}
.writtenTitle {
  font-size: 2.894vw;
  font-family: 'SignPainter';
  font-weight: 400;
  line-height: 2.2;
}
.subTitle {
  font-size: 1.736vw;
  line-height: 1.4;
  font-family: "Great Vibes", Arial;
  font-weight: 300;
  margin-bottom: 1.157vw;
}
.subTitle.primary {
  color: #2E5A3E;
}
.subTitle.secondary {
  color: #8B4513;
}
.textTitle {
  font-size: 0.926vw;
  line-height: 1.4;
  font-family: "Liter", Arial;
  font-weight: 300;
}
.textTitle.upper {
  font-size: 0.81vw;
  text-transform: uppercase;
}
.textTitle.smaller {
  font-size: 0.81vw;
}
.textTitle.primary {
  color: #2E5A3E;
}
.textTitle.secondary {
  color: #8B4513;
}
.text.bigger p {
  font-size: 1.736vw;
  line-height: 1.2;
}
.text.white p {
  color: #666666;
}
.text:not(:first-child) {
  margin-top: 1.157vw;
}
.text p {
  line-height: 1.4;
}
.text p:not(:last-child) {
  margin-bottom: 1.273vw;
}
@media all and (max-width: 1160px) {
  .hugeTitle {
    font-size: 12.069vw;
  }
  .biggerTitle {
    font-size: 6.724vw;
  }
  .bigTitle {
    font-size: 3.621vw;
  }
  .title {
    font-size: 4.31vw;
  }
  .smallTitle {
    font-size: 2.414vw;
  }
  .writtenTitle {
    font-size: 4.31vw;
  }
  .subTitle {
    font-size: 2.586vw;
    margin-bottom: 1.724vw;
  }
  .textTitle {
    font-size: 1.379vw;
  }
  .textTitle.upper {
    font-size: 1.379vw;
  }
  .textTitle.smaller {
    font-size: 1.379vw;
  }
  .text.bigger p {
    font-size: 2.586vw;
  }
  .text:not(:first-child) {
    margin-top: 1.724vw;
  }
  .text p:not(:last-child) {
    margin-bottom: 1.897vw;
  }
}
@media all and (max-width: 580px) {
  .hugeTitle {
    font-size: 24.138vw;
  }
  .biggerTitle {
    letter-spacing: -2px;
    font-size: 8.62vw;
  }
  .biggerTitle strong {
    letter-spacing: -5px;
    font-size: 20.69vw;
  }
  .bigTitle {
    font-size: 7.241vw;
  }
  .title {
    font-size: 8.62vw;
  }
  .smallTitle {
    font-size: 4.827vw;
  }
  .writtenTitle {
    font-size: 8.62vw;
  }
  .subTitle {
    font-size: 5.862vw;
    margin-bottom: 3.448vw;
  }
  .textTitle {
    font-size: 3.448vw;
  }
  .textTitle.upper {
    font-size: 3.448vw;
  }
  .textTitle.smaller {
    font-size: 3.448vw;
  }
  .text.bigger p {
    font-size: 4.137vw;
  }
  .text:not(:first-child) {
    margin-top: 3.448vw;
  }
  .text p:not(:last-child) {
    margin-bottom: 3.793vw;
  }
}
