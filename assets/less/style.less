// out: ../../style.css, compress: true, strictMath: true

/*
Theme Name: Lindenhof
Author: <PERSON>
Version: 1.0.0
*/

@import 'vw_values.less';
@import 'constants.less'; 
@import 'default.less';  
@import 'typo.less';
@import 'parts/header.less';  
@import 'parts/footer.less';
@import 'parts/openingtimes.less';
@import 'parts/ddsignature.less'; 
 
// include blocks
@import '../../template-parts/blocks/less/images-marquee-block.less';
@import '../../template-parts/blocks/less/huurkalender-block.less';

::-webkit-scrollbar {
  width: @vw10;
}

::-webkit-scrollbar-track {
  background: @almostWhite; 
}

::-webkit-scrollbar-thumb {
  border-radius: @vw50;
  background: rgba(0,0,0,.1);
}

.block__headline {
    padding: 20px 15px 30px;
    background: #fafafa;
    text-align: center;
} 
.block__headline-title {
    font-family: 'Arial', sans-serif;
    font-size: 30px; 
    font-weight: bold;
    position: relative;
}
.block__headline-title:after {
    content: '';
    display: block;
    width: 40px; 
    height: 2px;
    background: #333;  
    margin: 0 auto;
}
 
html.has-scroll-smooth {
	backface-visibility: hidden;
	transform: translateZ(0);
  [data-load-container] {
  	position: fixed;
  	top: 0;
  	right: 0;
  	bottom: 0;
  	left: 0;
  	width: 100vw;
  }
}

// Swup

.transition-fade {
  .transitionMore(opacity, .75s, 0s, cubic-bezier(.4, 0, .2, 1));
  opacity: 1;
}

html.is-animating .transition-fade {
  opacity: 0;
}

.grecaptcha-badge {
  visibility: hidden;
}