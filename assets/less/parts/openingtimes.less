// out: false

.openingstijden {
    width: 100%;
    border-collapse: collapse;
    th, td {
        padding: @vw5 0;
        padding-right: @vw10;
        text-align: left;
    }
    td {
        font-size: @vw16;
    }
    td:contains("Gesloten") {
        color: @secondaryColor;
    }
    .today {
        color: @primaryColor;
        &.closed {
            color: @secondaryColor;
        }
    }
    
}
@media (max-width: 1160px) {
    .openingstijden {
      th, td {
        padding: @vw5-1160 0;
        padding-right: @vw10-1160;
      }
      td {
        font-size: @vw16-1160;
      }
    }
  }
  
@media (max-width: 580px) {
    .openingstijden {
      th, td {
        padding: @vw5-580 0;
        padding-right: @vw10-580;
      }
      td {
        font-size: @vw20-580;
      }
    }
  }
  