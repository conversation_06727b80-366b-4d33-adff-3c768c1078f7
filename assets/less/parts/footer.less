// out: false
.footer {
  padding-top: @vw100 + @vw50;
  padding-bottom: @vw30;
  background: @almostWhite;
  bottom: 0;
  width: 100%;
  .cols {
    margin-left: -@vw8;
    width: calc(100% ~"+" @vw16);
    .col {
      display: inline-block;
      margin: 0 @vw8;
      vertical-align: top;
      nav {
        ul {
          list-style: none;
        }
        li {
          a {
            color: @hardBlack;
            .transition(.3s);
            &:hover {
              opacity: .5;
            }
          }
        }
      }
    }
  }
  .logo {
    margin-bottom: @vw35;
    display: block;
    width: @vw100 + @vw30;
    cursor: pointer;
    display: block;
    .transitionMore(opacity, .3s);
    &:hover {
      opacity: .7;
    }
    img {
      cursor: pointer;
      height: auto;
      width: 100%;
      display: block;
    }
  }
  .topFooter {
    .col {
      width: calc(25% ~"-" @vw16);
    }
    nav {
      ul {
       line-height: 1.8;
      }
    }
    .contactDetails {
      display: block;
      width: (@vw112 * 2) + @vw16;
      max-width: 100%;
      .links {
        margin: @vw30 0;
        .link {
          &:not(:last-child){
            margin-bottom: @vw10;
          }
          display: table;
          text-decoration: none;
          color: @hardBlack;
          cursor: pointer;
          .transition(.3s);
          &:hover {
            opacity: .7;
          }
          i {
            margin-right: @vw5;
          }
          span {
            text-decoration: underline;
          }
        }
      }
    }
    .socials {
      margin-top: @vw20;
      display: inline-block;
      vertical-align: middle;
      .social {
        display: inline-block;
        color: @hardBlack;
        padding: @vw10;
        .transitionMore(opacity, .3s);
        &:hover {
          opacity: .5;
        }
      }
    }
  }
  .bottomFooter {
    font-size: @vw16;
    padding-top: @vw16;
    margin-top: @vw36;
    border-top: 1px dashed rgba(0,0,0,.4);
    .col {
      width: calc(33.3333% ~"-" @vw16);
      vertical-align: middle;
      &:nth-child(2) {
        text-align: center;
      }
      &:nth-child(3) {
        text-align: right;
      }
    }
    li {
      display: inline-block;
      vertical-align: middle;
    }
    .innerDivider {
      display: inline-block;
      margin: 0 @vw5;
      vertical-align: middle;
      opacity: .2;
    }
    .divider {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      .innerCol {
        display: inline-block;
        position: relative;
        &:nth-child(1), &:nth-child(3) {
          width: 100%;
          .innerBar {
            background: @hardBlack;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            left: 0;
            width: 100%;
            height: 1px;
          }
        }
        &:nth-child(1) {
          .innerBar {
            right: 0;
            left: auto;
            -webkit-mask-image: linear-gradient(-90deg, rgba(0,0,0,1), rgba(0,0,0,0));
            mask-image: linear-gradient(-90deg, rgba(0,0,0,1), rgba(0,0,0,0));
          }
        }
        &:nth-child(2) {
          text-align: center;
          width: @vw60 + @vw5;
        }
        &:nth-child(3) {
          .innerBar {
            -webkit-mask-image: linear-gradient(90deg, rgba(0,0,0,1), rgba(0,0,0,0));
            mask-image: linear-gradient(90deg, rgba(0,0,0,1), rgba(0,0,0,0));
          }
        }
        i {
          display: block;
          font-size: @vw16;
          color: @hardBlack;
        }
      }
    }
    .bigLogoWrapper {
      position: absolute;
      width: (@vw112 * 6) + (@vw16 * 5);
      max-width: 100%;
      top: 0;
      left: 0;
      right: 0;
      margin: auto;
      opacity: .05;
      animation: rotate360 60s linear infinite;
      z-index: -1;
      pointer-events: none;
      svg {
        width: 100%;
        height: 100%;
        object-fit: cover;
        path {
          fill: @hardBlack;
        }
      }
    }
  }
}

@media (max-width: 1160px) {
  .footer {
      padding-top: @vw100-1160;
      padding-bottom: @vw30-1160;
      .cols {
          margin-left: -@vw8-1160;
          width: calc(100% ~"+" @vw16-1160);
          .col {
              margin: 0 @vw8-1160;
          }
      }
      .logo {
          margin-bottom: @vw35-1160;
          width: @vw100-1160 + @vw30-1160;
      }
      .topFooter {
          .col {
              width: calc(25% ~"-" @vw16-1160);
          }
          .contactDetails {
              width: (@vw112-1160 * 2) + @vw16-1160;
          }
          .socials {
              margin-top: @vw20-1160;
          }
      }
      .bottomFooter {
          font-size: @vw16-1160;
          padding-top: @vw16-1160;
          margin-top: @vw36-1160;
          .col {
              width: calc(33.3333% ~"-" @vw16-1160);
          }
          .divider {
              .innerCol {
                  width: @vw60-1160 + @vw5-1160;
              }
          }
          .innerDivider {
            margin: 0 @vw5-1160;
          }
      }
  }
}

@media (max-width: 580px) {
  .footer {
      padding-top: @vw100-580;
      padding-bottom: @vw30-580;
      .cols {
          margin-left: -@vw8-580;
          width: calc(100% ~"+" @vw16-580);
          .col {
              margin: 0 @vw8-580;
          }
      }
      .logo {
          margin-bottom: @vw35-580;
          width: @vw100-580 + @vw30-580;
      }
      .topFooter {
          .col {
              width: calc(50% ~"-" @vw16-580);
              &:nth-child(2), &:nth-child(3) {
                display: none;
              }
          }
          .contactDetails {
              width: (@vw112-580 * 2) + @vw16-580;
          }
          .socials {
              margin-top: @vw20-580;
          }
      }
      .openingstijden {
        td {
          padding-right: 0 !important;
          &:nth-child(even) {
            padding-left: @vw8-580 !important;
          }
        }
      }
      .bottomFooter {
          font-size: @vw22-580;
          padding-top: @vw22-580;
          margin-top: @vw24-580;
          .col {
            text-align: left;
            width: calc(50% ~"-" @vw16-580);
            &:first-child {
              width: calc(100% ~"-" @vw16-580);
              margin-bottom: @vw22-580;
            }
            &:nth-child(2) {
              text-align: left;
            }
          }
          .year {
            display: none;
          }
          .divider {
              .innerCol {
                  width: @vw60-580 + @vw5-580;
              }
          }
          .innerDivider {
            margin: 0 @vw12-580;
          }
      }
  }
}
