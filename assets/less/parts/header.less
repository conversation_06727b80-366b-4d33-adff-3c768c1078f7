// out: false
header {
    position: fixed;
    top: @vw20;
    width: calc(100% ~"-" @vw80 ~"-" @vw80);
    left: @vw80;
    z-index: 99;
    height: @vw74;
    padding: 0 @vw24;
    &.active {
      .cols {
        .col {
          opacity: 1;
          .transform(translateY(0));
          transition: opacity .6s 0s cubic-bezier(0.34, 1.3, 0.64, 1), transform .3s 0s cubic-bezier(0.34, 1.64, 0.64, 1), pointer-events .3s 0s ease-in-out;
          pointer-events: all;
          .stagger(3, 0.15s, .6s);
        }
      }
      &:before {
        .transform(translateX(-50%) scale(1));
        width: 100%;
        transition: width .6s .3s cubic-bezier(0.34, 1.3, 0.64, 1), transform .3s 0s cubic-bezier(0.34, 1.64, 0.64, 1);
        will-change: width, transform;
      }
    }
    &:before {
      content: '';
      position: absolute;
      left: 50%;
      .transform(translateX(-50%) scale(0));
      top: 0;
      margin: auto;
      box-shadow: 0 0 @vw50 rgba(0,0,0,.05);
      display: block;
      text-align: center;
      .rounded(@vw74);
      transform-origin: center;
      background: @hardWhite;
      width: @vw74;
      height: 100%;
    }
    .hamburger {
      display: none;
    }
    .cols {
      .col {
        display: inline-block;
        vertical-align: middle;
        opacity: 0;
        pointer-events: none;
        .transform(translateY(@vw22));
        &:first-child {
          width: auto;
        }
        &:nth-child(2) {
          width: 60%;
        }
        &:last-child {
          text-align: right;
          width: calc(40% ~"-" @vw100 ~"-" @vw60);
        }
        .socials {
          display: inline-block;
          vertical-align: middle;
          margin-right: @vw30;
          .social {
            display: inline-block;
            color: @primaryColor;
            padding: @vw10;
            .transitionMore(opacity, .3s);
            &:hover {
              opacity: .6;
            }
            i {
              cursor: pointer;
            }
          }
        }
        .logo {
          margin-right: @vw30;
          display: block;
          width: @vw100 + @vw30;
          cursor: pointer;
          display: block;
          .transitionMore(opacity, .3s);
          &:hover {
            opacity: .7;
          }
          img {
            cursor: pointer;
            height: auto;
            width: 100%;
            display: block;
          }
        }
        ul {
          list-style: none;
          line-height: @vw74;
        }
        .menu {
          list-style-type: none;
          padding: 0;
          margin: 0;
          display: flex;
          li {
            position: relative;
            a {
              .transitionMore(color, .15s);
              padding: @vw10 @vw20;
              text-decoration: none;
              color: @hardBlack;
              &:hover {
                color: @primaryColor;
              }
            }
  
            /* Submenu-styling */
            &.has-submenu {
              > a {
                position: relative;
  
                &::after {
                  content: '';
                  position: absolute;
                  right: 10px;
                  top: 50%;
                  transform: translateY(-50%);
                  border: solid transparent;
                  border-width: 5px 5px 0;
                }
              }
              .submenu {
                opacity: 0;
                visibility: hidden;
                pointer-events: none;
                display: block;
                position: absolute;
                top: 100%;
                left: 0;
                z-index: 10;
                line-height: 2.4;
                margin-top: 0;
                width: @vw100 + @vw100 + @vw50;
                .innerWrapper {
                  display: block;
                  padding: @vw10 @vw30;
                  background-color: #fff;
                  .rounded(@vw20);
                  .transform(translateY(@vw22));
                  transition: transform .3s, opacity .3s, visibility .3s;
                  will-change: transform;
                }
                li {
                  a {
                    padding: @vw10 0;
                    text-decoration: none;
                    color: @hardBlack;
                    &:hover {
                      color: @primaryColor;
                    }
                  }
                }
              }
  
              &:hover {
                .submenu {
                  opacity: 1;
                  visibility: visible;
                  pointer-events: all;
                  .innerWrapper {
                    .transform(translateY(@vw10));
                  }
                }
              }
            }
          }
        }
      }
    }
    .mobileMenu {
      display: none;
      visibility: hidden;
      opacity: 0;
    }
}

@media (max-width: 1160px) {
  header {
    top: @vw20-1160;
    width: calc(100% - @vw80-1160);
    left: @vw40-1160;
    height: @vw74-1160;
    padding: 0 @vw24-1160;
    line-height: @vw74-1160;
    &:before {
      .rounded(@vw74-1160);
    }
    .cols {
      .col {
        .transform(translateY(@vw22-1160));
        &:last-child {
          width: calc(33.3333% - @vw100-1160 - @vw60-1160);
          .socials {
            line-height: 1;
            .social {
              padding: @vw10-1160;
            }
          }
          .button {
            display: none;
          }
        }
        .logo {
          width: @vw100-1160 + @vw30-1160;
          margin-right: @vw30-1160;
        }
        .socials {
          margin-right: 0;
        }
        .menu li a {
          padding: @vw10-1160 @vw20-1160;
        }
      }
    }
  }
}

@media (max-width: 580px) {
  header {
    top: @vw20-580;
    width: calc(100% ~"-" @vw40-580);
    left: @vw20-580;
    height: @vw74-580;
    line-height: @vw74-580;
    padding: 0 @vw24-580;
    .rounded(@vw100-580);
    &.open {
      .hamburger {
        .border {
          background: @hardWhite;
          &:nth-child(1) {
            .transform(translateY(@vw6-580) rotate(-45deg));
          }
          &:nth-child(2) {
            width: 0%;
          }
          &:nth-child(3) {
            .transform(translateY(-@vw6-580) rotate(45deg));
          }
        }
      }
    }
    .hamburger {
      cursor: pointer;
      display: inline-block;
      height: @vw64-580;
      vertical-align: middle;
      width: @vw64-580;
      .rounded(50%);
      background: @primaryColor;
      .innerBurger {
        pointer-events: none;
        position: absolute;
        top: 50%;
        left: 50%;
        .transform(translate(-50%,-50%));
        height: @vw14-580;
        width: @vw22-580;
      }
      .border {
        position: absolute;
        display: block;
        height: 2px;
        width: 100%;
        border-radius: @vw2-580;
        background: @hardWhite;
        .transition(.3s);
        &:first-child {
          top: 0;
        }
        &:nth-child(2) {
          top: 50%;
          transform: translateY(-50%);
        }
        &:nth-child(3) {
          bottom: 0;
          top: auto;
        }
      }
    }
    &:before {
      .rounded(@vw100-580);
    }
    .cols {
      .col {
        .transform(translateY(@vw22-580));
        &:nth-child(2) {
          display: none;
        }
        &:last-child {
          width: calc(100% ~"-" @vw100-580 ~"-" @vw60-580);
          .socials {
            .social {
              padding: @vw12-580;
            }
          }
        }
        .logo {
          margin-right: @vw30-580;
          width: @vw100-580 + @vw30-580;
        }
        .socials {
          margin-right: @vw30-580;
        }
        .menu li a {
          padding: @vw10-580 @vw20-580;
        }
      }
    }
  }
  .mobileMenu {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: @hardWhite;
    overflow-y: scroll;
    color: @hardBlack;
    z-index: 98;
    padding: @vw120-580 @vw40-580 @vw100-580 + @vw50-580 @vw40-580;
    display: block !important;
    visibility: visible;
    opacity: 0;
    pointer-events: none;
    .transitionMore(opacity, .45s, .45s, ease-in-out);
    &.open {
      pointer-events: all;
      opacity: 1;
      .transitionMore(opacity, .45s, 0s, ease-in-out);
      nav {
        opacity: 1;
        .transform(translateY(0));
        transition: transform .45s .6s ease-in-out, opacity .45s .6s ease-in-out;
      }
    }
    nav {
      opacity: 0;
      .transform(translateY(@vw20-580));
      transition: transform .45s 0s ease-in-out, opacity .45s 0s ease-in-out;
    }
    ul {
      list-style: none;
    }
    .submenu {
      margin-top: @vw12-580;
    }
    li {
      position: relative;
      &:not(:last-child) {
        margin-bottom: @vw12-580;
      }
      a {
        .transitionMore(color, .15s);
        padding: @vw10-580 0;
        display: block;
        text-decoration: none;
        font-size: @vw30-580;
        color: @hardBlack;
        &:hover {
          color: @primaryColor;
        }
      }
    }
    .innerWrapper {
      display: block;
      padding-bottom: @vw20-580;
      margin-bottom: @vw26-580;
      border-bottom: 1px solid rgba(0,0,0,.1);
    }
  }
}
