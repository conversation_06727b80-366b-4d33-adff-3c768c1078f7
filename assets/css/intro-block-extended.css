/**
 * Extended Intro Block Styling
 * Google Reviews, Content Sections, and Sticky Booking Widget
 *
 * @package Lindenhof
 * @version 1.0.0
 */

/* Header Info Bar */
.lindenhof-intro-block__header-info {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 20px;
    padding: 15px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    flex-wrap: wrap;
}

.lindenhof-intro-block__header-reviews,
.lindenhof-intro-block__header-location,
.lindenhof-intro-block__header-capacity,
.lindenhof-intro-block__header-beds {
    display: flex;
    align-items: center;
    gap: 8px;
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.9rem;
    font-weight: 500;
}

.lindenhof-intro-block__header-reviews {
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 12px;
    border-radius: 20px;
    backdrop-filter: blur(10px);
}

.lindenhof-intro-block__header-rating {
    display: flex;
    align-items: center;
    gap: 8px;
}

.lindenhof-intro-block__header-rating .google-stars {
    gap: 1px;
}

.lindenhof-intro-block__header-rating .google-stars .star {
    font-size: 1rem;
    color: #ffc107;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.lindenhof-intro-block__header-rating-number {
    font-weight: 600;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.lindenhof-intro-block__location-icon,
.lindenhof-intro-block__capacity-icon,
.lindenhof-intro-block__beds-icon {
    font-size: 1.1rem;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.lindenhof-intro-block__location-text,
.lindenhof-intro-block__capacity-text,
.lindenhof-intro-block__beds-text {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Google Reviews Section */
.lindenhof-intro-block__google-reviews {
    margin: 40px 0;
    padding: 30px;
    background: #f8f9fa;
    border-radius: 12px;
    text-align: center;
}

.lindenhof-intro-block__reviews-loading {
    color: #666;
    font-style: italic;
}

.lindenhof-intro-block__reviews-summary {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin-bottom: 20px;
}

.lindenhof-intro-block__rating-display {
    display: flex;
    align-items: center;
    gap: 10px;
}

.lindenhof-intro-block__rating-number {
    font-size: 2rem;
    font-weight: bold;
    color: #333;
}

.google-stars {
    display: flex;
    gap: 2px;
}

.google-stars .star {
    font-size: 1.5rem;
    color: #ddd;
    transition: color 0.3s ease;
}

.google-stars .star--full {
    color: #ffc107;
}

.google-stars .star--half {
    background: linear-gradient(90deg, #ffc107 50%, #ddd 50%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.lindenhof-intro-block__reviews-count {
    color: #666;
    font-size: 0.9rem;
}

.lindenhof-intro-block__google-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: #4285f4;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.lindenhof-intro-block__google-link:hover {
    color: #1a73e8;
}

/* Content Sections */
.lindenhof-intro-block__content-sections {
    margin: 50px 0;
}

.lindenhof-intro-block__sections-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin-top: 30px;
}

.lindenhof-intro-block__section {
    background: #fff;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.lindenhof-intro-block__section:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.lindenhof-intro-block__section-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
}

.lindenhof-intro-block__section--faciliteiten .lindenhof-intro-block__section-title {
    border-bottom-color: #28a745;
}

.lindenhof-intro-block__section--eten_drinken .lindenhof-intro-block__section-title {
    border-bottom-color: #fd7e14;
}

.lindenhof-intro-block__section--omgeving .lindenhof-intro-block__section-title {
    border-bottom-color: #20c997;
}

.lindenhof-intro-block__section--algemeen .lindenhof-intro-block__section-title {
    border-bottom-color: #6f42c1;
}

.lindenhof-intro-block__facilities-preview {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.lindenhof-intro-block__facility-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 0;
}

.lindenhof-intro-block__facility-icon {
    font-size: 1.2rem;
    width: 24px;
    text-align: center;
}

.lindenhof-intro-block__facility-title {
    font-weight: 500;
    color: #333;
}

.lindenhof-intro-block__content-preview {
    color: #666;
    line-height: 1.6;
}

.lindenhof-intro-block__more-info {
    margin-top: 15px;
    font-style: italic;
    color: #888;
    font-size: 0.9rem;
}

/* Gallery Styling */
.lindenhof-intro-block__gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 30px 0;
}

.lindenhof-intro-block__gallery-item {
    border-radius: 8px;
    overflow: hidden;
    aspect-ratio: 4/3;
}

.lindenhof-intro-block__gallery-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.lindenhof-intro-block__gallery-item:hover .lindenhof-intro-block__gallery-image {
    transform: scale(1.05);
}

/* Sticky Booking Widget */
.lindenhof-intro-block__sticky-booking {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
}

.lindenhof-intro-block__booking-toggle {
    display: flex;
    align-items: center;
    gap: 10px;
    background: #007cba;
    color: white;
    border: none;
    border-radius: 50px;
    padding: 15px 25px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    box-shadow: 0 4px 15px rgba(0, 124, 186, 0.3);
    transition: all 0.3s ease;
}

.lindenhof-intro-block__booking-toggle:hover {
    background: #005a87;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 124, 186, 0.4);
}

.lindenhof-intro-block__booking-icon {
    font-size: 1.2rem;
}

.lindenhof-intro-block__booking-widget {
    position: absolute;
    bottom: 70px;
    right: 0;
    width: 400px;
    max-width: 90vw;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    transform: translateY(20px) scale(0.95);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.lindenhof-intro-block__sticky-booking.is-open .lindenhof-intro-block__booking-widget {
    transform: translateY(0) scale(1);
    opacity: 1;
    visibility: visible;
}

.lindenhof-intro-block__booking-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
}

.lindenhof-intro-block__booking-title {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
}

.lindenhof-intro-block__booking-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #666;
    cursor: pointer;
    padding: 5px;
    line-height: 1;
    transition: color 0.3s ease;
}

.lindenhof-intro-block__booking-close:hover {
    color: #333;
}

.lindenhof-intro-block__booking-content {
    padding: 20px;
    max-height: 500px;
    overflow-y: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
    .lindenhof-intro-block__sections-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .lindenhof-intro-block__gallery {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }
    
    .lindenhof-intro-block__sticky-booking {
        bottom: 10px;
        right: 10px;
    }
    
    .lindenhof-intro-block__booking-widget {
        width: 350px;
        bottom: 60px;
    }
    
    .lindenhof-intro-block__booking-toggle {
        padding: 12px 20px;
        font-size: 0.9rem;
    }
    
    .lindenhof-intro-block__reviews-summary {
        flex-direction: column;
        gap: 10px;
    }
    
    .lindenhof-intro-block__rating-number {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .lindenhof-intro-block__gallery {
        grid-template-columns: 1fr;
    }

    .lindenhof-intro-block__booking-widget {
        width: calc(100vw - 20px);
        right: 10px;
        left: 10px;
    }

    .lindenhof-intro-block__header-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}

/* Faciliteiten Block Styling */
.lindenhof-faciliteiten-block__categories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 30px;
}

.lindenhof-faciliteiten-block__category {
    background: #fff;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.lindenhof-faciliteiten-block__category-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e9ecef;
}

.lindenhof-faciliteiten-block__items {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.lindenhof-faciliteiten-block__item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 0;
    transition: background-color 0.3s ease;
}

.lindenhof-faciliteiten-block__item:hover {
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: 6px;
    padding-left: 8px;
    padding-right: 8px;
}

.lindenhof-faciliteiten-block__item-icon {
    font-size: 1.1rem;
    font-weight: bold;
    width: 20px;
    text-align: center;
    flex-shrink: 0;
}

.lindenhof-faciliteiten-block__item--yes .lindenhof-faciliteiten-block__item-icon {
    color: #28a745;
}

.lindenhof-faciliteiten-block__item--no .lindenhof-faciliteiten-block__item-icon {
    color: #dc3545;
}

.lindenhof-faciliteiten-block__item-label {
    color: #333;
    font-weight: 500;
    line-height: 1.4;
}

.lindenhof-faciliteiten-block__item--no .lindenhof-faciliteiten-block__item-label {
    color: #666;
    text-decoration: line-through;
}

@media (max-width: 768px) {
    .lindenhof-faciliteiten-block__categories {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .lindenhof-faciliteiten-block__category {
        padding: 20px;
    }
}
