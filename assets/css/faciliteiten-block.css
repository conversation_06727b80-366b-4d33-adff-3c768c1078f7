/* Faciliteiten Block - Simple 3 column layout */
.faciliteiten-block {
    padding: 60px 0;
}

.faciliteiten-block h2 {
    text-align: center;
    margin-bottom: 40px;
    font-size: 2rem;
    color: #333;
}

.faciliteiten-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 40px;
    max-width: 1200px;
    margin: 0 auto;
}

.faciliteiten-column h3 {
    font-size: 1.25rem;
    margin-bottom: 20px;
    color: #333;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
}

.faciliteiten-column ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.faciliteiten-column li {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 0;
    border-bottom: 1px solid #f8f9fa;
}

.faciliteiten-column li:last-child {
    border-bottom: none;
}

.faciliteiten-column .icon {
    font-size: 1.1rem;
    font-weight: bold;
    width: 20px;
    text-align: center;
}

.faciliteiten-column .yes .icon {
    color: #28a745;
}

.faciliteiten-column .no .icon {
    color: #dc3545;
}

.faciliteiten-column .no {
    color: #666;
    text-decoration: line-through;
}

.faciliteiten-column .yes {
    color: #333;
}

/* Mobile responsive */
@media (max-width: 768px) {
    .faciliteiten-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    
    .faciliteiten-block {
        padding: 40px 0;
    }
}
