let lastScrollY = 0;
let scrollDirection = 'up';

$(document).ready(function(){
  $(document).on("initPage", function () {
    setAnchorPoints();
    setScrolled();
    initHeaderScrollBehavior();
  });
});

function setScrolled() {
  if (currentScrollY > 100) {
    $("header").addClass("scrolled");
  } else {
    $("header").removeClass("scrolled");
  }
  scroller.on("scroll", function () {
    if (currentScrollY > 100) {
      $("header").addClass("scrolled");
    } else {
      $("header").removeClass("scrolled");
    }
  });
}

function initHeaderScrollBehavior() {
  scroller.on("scroll", function () {
    // Determine scroll direction
    if (currentScrollY > lastScrollY && currentScrollY > 100) {
      // Scrolling down
      if (scrollDirection !== 'down') {
        scrollDirection = 'down';
        $("header").addClass("hidden");
      }
    } else if (currentScrollY < lastScrollY) {
      // Scrolling up
      if (scrollDirection !== 'up') {
        scrollDirection = 'up';
        $("header").removeClass("hidden");
      }
    }

    // Always show header when at top
    if (currentScrollY <= 100) {
      $("header").removeClass("hidden");
      scrollDirection = 'up';
    }

    lastScrollY = currentScrollY;
  });
}

function setAnchorPoints() {
  if (!$("#headerContainer").hasClass("active")) {
    $("#headerContainer").addClass("active");
  }
  $(".anchorMenu").each(function () {
    var $anchorMenu = $(this);
    $($anchorMenu).html("");
    $("[data-anchor]").each(function () {
      const $section = $(this);
      const anchorName = $section.data("anchor");

      const $menuItem = $(`<a href="#${anchorName}" class="menu-item">${anchorName}</a>`);
      $anchorMenu.append($menuItem);
    });

    $anchorMenu.on("click touchend", ".menu-item", function (event) {
      const targetAnchor = $(this).attr("href");
      if (!targetAnchor) return;

      event.preventDefault();
      const targetID = targetAnchor.slice(1);
      const $targetSection = $(`[data-anchor="${targetID}"]`);

      if ($targetSection.length) {
        scroller.scrollTo($targetSection[0], {
          offset: 0,
          duration: 1.2,
          easing: (t) => 1 - Math.pow(1 - t, 4),
        });
      }
    });
  });

  $(document).on("click touchend", ".button", function (event) {
    const targetAnchor = $(this).attr("href");
    if (!targetAnchor) return;

    // Don't intercept external links or links with data-no-swup attribute
    if ($(this).attr("target") === "_blank" || $(this).attr("data-no-swup") === "true") {
      return; // Let the browser handle the navigation
    }

    // Don't intercept external URLs (starting with http:// or https://)
    if (targetAnchor.startsWith("http://") || targetAnchor.startsWith("https://")) {
      return; // Let the browser handle the navigation
    }

    event.preventDefault();
    const targetID = targetAnchor.slice(1);
    const $targetSection = $(`[data-anchor="${targetID}"]`);

    if ($targetSection.length) {
      scroller.scrollTo($targetSection[0], {
        offset: 0,
        duration: 1.2,
        easing: (t) => 1 - Math.pow(1 - t, 4),
      });
    }
  });
}
