/**
 * Lindenhof Blocks JavaScript
 * 
 * @package Lindenhof
 * @version 1.0.0
 */

(function($) {
    'use strict';
    
    // Main Lindenhof object
    window.Lindenhof = window.Lindenhof || {};
    
    // Configuration
    Lindenhof.config = {
        debug: false,
        swupEnabled: true,
        gsapEnabled: true,
        animationDuration: 0.6,
        animationEase: 'power2.out'
    };
    
    // Utility functions
    Lindenhof.utils = {
        
        // Debug logging
        log: function(message, data) {
            if (Lindenhof.config.debug && console && console.log) {
                console.log('[Lindenhof] ' + message, data || '');
            }
        },
        
        // Check if element exists
        exists: function(selector) {
            return $(selector).length > 0;
        },
        
        // Generate unique ID
        uniqueId: function(prefix) {
            prefix = prefix || 'lindenhof';
            return prefix + '-' + Math.random().toString(36).substr(2, 9);
        },
        
        // Debounce function
        debounce: function(func, wait, immediate) {
            var timeout;
            return function() {
                var context = this, args = arguments;
                var later = function() {
                    timeout = null;
                    if (!immediate) func.apply(context, args);
                };
                var callNow = immediate && !timeout;
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
                if (callNow) func.apply(context, args);
            };
        }
    };
    
    // Block initialization functions
    Lindenhof.blocks = {
        
        // Initialize all blocks
        init: function() {
            Lindenhof.utils.log('Initializing blocks');
            
            this.initIntroBlocks();
            this.initFaciliteitenBlocks();
            this.initAlgemeenBlocks();
            this.initEtenDrinkenBlocks();
            this.initOmgevingBlocks();
            this.initHuurkalenderBlocks();
            this.initImagesMarqueeBlocks();
            this.initContactBlocks();
        },
        
        // Intro blocks
        initIntroBlocks: function() {
            $('.intro-block, .lindenhof-intro-block').each(function() {
                var $block = $(this);
                Lindenhof.utils.log('Initializing intro block', $block.attr('id'));

                // Initialize Google Reviews
                Lindenhof.blocks.initGoogleReviews($block);

                // Initialize sticky booking widget
                Lindenhof.blocks.initStickyBooking($block);

                // Fade in animation
                if (Lindenhof.config.gsapEnabled && window.gsap) {
                    gsap.from($block.find('.intro-block__title, .intro-block__subtitle, .intro-block__content, .lindenhof-intro-block__title, .lindenhof-intro-block__subtitle, .lindenhof-intro-block__content'), {
                        duration: Lindenhof.config.animationDuration,
                        y: 30,
                        opacity: 0,
                        stagger: 0.2,
                        ease: Lindenhof.config.animationEase
                    });
                }
            });
        },
        
        // Faciliteiten blocks
        initFaciliteitenBlocks: function() {
            $('.faciliteiten-block').each(function() {
                var $block = $(this);
                Lindenhof.utils.log('Initializing faciliteiten block', $block.attr('id'));
                
                // Staggered animation for facility items
                if (Lindenhof.config.gsapEnabled && window.gsap) {
                    gsap.from($block.find('.faciliteiten-block__item'), {
                        duration: Lindenhof.config.animationDuration,
                        y: 20,
                        opacity: 0,
                        stagger: 0.1,
                        ease: Lindenhof.config.animationEase,
                        delay: 0.2
                    });
                }
            });
        },
        
        // Algemeen blocks
        initAlgemeenBlocks: function() {
            $('.algemeen-block').each(function() {
                var $block = $(this);
                Lindenhof.utils.log('Initializing algemeen block', $block.attr('id'));
                
                // Fade in content and image
                if (Lindenhof.config.gsapEnabled && window.gsap) {
                    gsap.from($block.find('.algemeen-block__content, .algemeen-block__image'), {
                        duration: Lindenhof.config.animationDuration,
                        y: 30,
                        opacity: 0,
                        stagger: 0.3,
                        ease: Lindenhof.config.animationEase
                    });
                }
            });
        },
        
        // Eten & Drinken blocks
        initEtenDrinkenBlocks: function() {
            $('.eten-drinken-block').each(function() {
                var $block = $(this);
                Lindenhof.utils.log('Initializing eten-drinken block', $block.attr('id'));
                
                // Animate restaurant items
                if (Lindenhof.config.gsapEnabled && window.gsap) {
                    gsap.from($block.find('.eten-drinken-block__restaurant'), {
                        duration: Lindenhof.config.animationDuration,
                        y: 20,
                        opacity: 0,
                        stagger: 0.15,
                        ease: Lindenhof.config.animationEase,
                        delay: 0.2
                    });
                }
            });
        },
        
        // Omgeving blocks
        initOmgevingBlocks: function() {
            $('.omgeving-block').each(function() {
                var $block = $(this);
                Lindenhof.utils.log('Initializing omgeving block', $block.attr('id'));
                
                // Animate activity items
                if (Lindenhof.config.gsapEnabled && window.gsap) {
                    gsap.from($block.find('.omgeving-block__activity'), {
                        duration: Lindenhof.config.animationDuration,
                        y: 20,
                        opacity: 0,
                        stagger: 0.1,
                        ease: Lindenhof.config.animationEase,
                        delay: 0.2
                    });
                }
            });
        },
        
        // Huurkalender blocks
        initHuurkalenderBlocks: function() {
            $('.huurkalender-block').each(function() {
                var $block = $(this);
                var blockId = $block.attr('id');
                Lindenhof.utils.log('Initializing huurkalender block', blockId);

                // Initialize Huurkalender.nl widgets
                Lindenhof.huurkalender.initHuurkalenderWidgets($block);
                
                // Fade in animation
                if (Lindenhof.config.gsapEnabled && window.gsap) {
                    gsap.from($block.find('.huurkalender-block__booking-form, .huurkalender-block__calendar'), {
                        duration: Lindenhof.config.animationDuration,
                        y: 30,
                        opacity: 0,
                        stagger: 0.3,
                        ease: Lindenhof.config.animationEase
                    });
                }
            });
        },
        
        // Images Marquee blocks
        initImagesMarqueeBlocks: function() {
            $('.images-marquee-block').each(function() {
                var $block = $(this);
                Lindenhof.utils.log('Initializing images-marquee block', $block.attr('id'));
                
                var $marquee = $block.find('.images-marquee-block__marquee');
                if ($marquee.length) {
                    Lindenhof.marquee.init($marquee);
                }
            });
        },
        
        // Contact blocks
        initContactBlocks: function() {
            $('.contact-block').each(function() {
                var $block = $(this);
                Lindenhof.utils.log('Initializing contact block', $block.attr('id'));

                // Fade in animation
                if (Lindenhof.config.gsapEnabled && window.gsap) {
                    gsap.from($block.find('.contact-block__info, .contact-block__form, .contact-block__map'), {
                        duration: Lindenhof.config.animationDuration,
                        y: 30,
                        opacity: 0,
                        stagger: 0.2,
                        ease: Lindenhof.config.animationEase
                    });
                }
            });
        },

        // Google Reviews
        initGoogleReviews: function($block) {
            var $reviewsContainer = $block.find('.lindenhof-intro-block__google-reviews');
            var $headerReviews = $block.find('.lindenhof-intro-block__header-reviews');

            if (!$reviewsContainer.length && !$headerReviews.length) return;

            var placeId = $reviewsContainer.length ? $reviewsContainer.data('place-id') : $headerReviews.data('place-id');
            if (!placeId) return;

            Lindenhof.utils.log('Loading Google Reviews for place ID:', placeId);

            // Make AJAX request to get reviews
            var restUrl = (typeof lindenhof_ajax !== 'undefined' && lindenhof_ajax.rest_url)
                ? lindenhof_ajax.rest_url
                : '/wp-json/lindenhof/v1/';

            $.ajax({
                url: restUrl + 'google-reviews/' + placeId,
                method: 'GET',
                timeout: 10000,
                success: function(data) {
                    if ($reviewsContainer.length) {
                        Lindenhof.blocks.renderGoogleReviews($reviewsContainer, data);
                    }
                    if ($headerReviews.length) {
                        Lindenhof.blocks.renderHeaderReviews($headerReviews, data);
                    }
                },
                error: function(xhr, status, error) {
                    Lindenhof.utils.log('Error loading Google Reviews:', error);
                    if ($reviewsContainer.length) {
                        $reviewsContainer.find('.lindenhof-intro-block__reviews-loading').html(
                            '<p style="color: #dc3545;">Kon Google Reviews niet laden.</p>'
                        );
                    }
                    if ($headerReviews.length) {
                        $headerReviews.find('.lindenhof-intro-block__header-reviews-loading').html(
                            '<span style="color: #dc3545;">✗</span>'
                        );
                    }
                }
            });
        },

        renderGoogleReviews: function($container, data) {
            var $loading = $container.find('.lindenhof-intro-block__reviews-loading');
            var $content = $container.find('.lindenhof-intro-block__reviews-content');

            if (!data.rating || data.rating === 0) {
                $loading.html('<p>Geen reviews beschikbaar.</p>');
                return;
            }

            // Generate stars HTML
            var starsHtml = Lindenhof.blocks.generateStarsHtml(data.rating);

            // Build reviews HTML
            var reviewsHtml = '<div class="lindenhof-intro-block__reviews-summary">' +
                '<div class="lindenhof-intro-block__rating-display">' +
                    '<span class="lindenhof-intro-block__rating-number">' + data.rating.toFixed(1) + '</span>' +
                    starsHtml +
                '</div>' +
                '<div class="lindenhof-intro-block__reviews-count">' +
                    'Gebaseerd op ' + data.total_ratings + ' reviews' +
                '</div>' +
            '</div>';

            if (data.google_url) {
                reviewsHtml += '<a href="' + data.google_url + '" target="_blank" rel="noopener" class="lindenhof-intro-block__google-link">' +
                    'Bekijk alle reviews op Google' +
                    '<span>→</span>' +
                '</a>';
            }

            $content.html(reviewsHtml);
            $loading.hide();
            $content.show();

            // Animate in
            if (Lindenhof.config.gsapEnabled && window.gsap) {
                gsap.from($content.children(), {
                    duration: 0.6,
                    y: 20,
                    opacity: 0,
                    stagger: 0.1,
                    ease: 'power2.out'
                });
            }
        },

        generateStarsHtml: function(rating) {
            var fullStars = Math.floor(rating);
            var hasHalfStar = (rating - fullStars) >= 0.5;
            var emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

            var html = '<div class="google-stars" data-rating="' + rating + '">';

            // Full stars
            for (var i = 0; i < fullStars; i++) {
                html += '<span class="star star--full">★</span>';
            }

            // Half star
            if (hasHalfStar) {
                html += '<span class="star star--half">★</span>';
            }

            // Empty stars
            for (var i = 0; i < emptyStars; i++) {
                html += '<span class="star star--empty">☆</span>';
            }

            html += '</div>';
            return html;
        },

        renderHeaderReviews: function($container, data) {
            var $loading = $container.find('.lindenhof-intro-block__header-reviews-loading');
            var $content = $container.find('.lindenhof-intro-block__header-reviews-content');

            if (!data.rating || data.rating === 0) {
                $loading.html('<span style="color: #999;">★★★★★</span>');
                return;
            }

            // Generate compact stars HTML
            var starsHtml = Lindenhof.blocks.generateStarsHtml(data.rating);

            // Build compact header reviews HTML
            var headerHtml = '<div class="lindenhof-intro-block__header-rating">' +
                starsHtml +
                '<span class="lindenhof-intro-block__header-rating-number">' + data.rating.toFixed(1) + '</span>' +
            '</div>';

            $content.html(headerHtml);
            $loading.hide();
            $content.show();

            // Animate in
            if (Lindenhof.config.gsapEnabled && window.gsap) {
                gsap.from($content, {
                    duration: 0.4,
                    opacity: 0,
                    scale: 0.9,
                    ease: 'power2.out'
                });
            }
        },

        // Sticky Booking Widget
        initStickyBooking: function($block) {
            var $stickyBooking = $block.find('.lindenhof-intro-block__sticky-booking');
            if (!$stickyBooking.length) return;

            var $toggle = $stickyBooking.find('.lindenhof-intro-block__booking-toggle');
            var $widget = $stickyBooking.find('.lindenhof-intro-block__booking-widget');
            var $close = $stickyBooking.find('.lindenhof-intro-block__booking-close');

            // Toggle widget
            $toggle.on('click', function(e) {
                e.preventDefault();
                $stickyBooking.toggleClass('is-open');

                if ($stickyBooking.hasClass('is-open')) {
                    $toggle.attr('aria-label', 'Booking widget sluiten');
                } else {
                    $toggle.attr('aria-label', 'Booking widget openen');
                }
            });

            // Close widget
            $close.on('click', function(e) {
                e.preventDefault();
                $stickyBooking.removeClass('is-open');
                $toggle.attr('aria-label', 'Booking widget openen');
            });

            // Close on escape key
            $(document).on('keydown', function(e) {
                if (e.key === 'Escape' && $stickyBooking.hasClass('is-open')) {
                    $stickyBooking.removeClass('is-open');
                    $toggle.attr('aria-label', 'Booking widget openen');
                }
            });

            // Close when clicking outside
            $(document).on('click', function(e) {
                if ($stickyBooking.hasClass('is-open') &&
                    !$stickyBooking.is(e.target) &&
                    $stickyBooking.has(e.target).length === 0) {
                    $stickyBooking.removeClass('is-open');
                    $toggle.attr('aria-label', 'Booking widget openen');
                }
            });

            Lindenhof.utils.log('Sticky booking widget initialized');
        }
    };
    
    // Huurkalender functionality
    Lindenhof.huurkalender = {

        // Initialize Huurkalender.nl widgets
        initHuurkalenderWidgets: function($block) {
            // The Huurkalender.nl embed scripts handle the initialization automatically
            // We just need to ensure the iframes are properly loaded

            var $iframes = $block.find('iframe[id*="iframe_huurkalender"]');

            $iframes.each(function() {
                var $iframe = $(this);
                var iframeId = $iframe.attr('id');

                // Log successful initialization
                $iframe.on('load', function() {
                    Lindenhof.utils.log('Huurkalender iframe loaded successfully', iframeId);
                });

                // Handle iframe errors
                $iframe.on('error', function() {
                    Lindenhof.utils.log('Error loading Huurkalender iframe', iframeId);
                    $iframe.closest('.huurkalender-embed').html(
                        '<p style="text-align: center; padding: 20px; color: #666;">Kalender kon niet worden geladen. Probeer de pagina te vernieuwen.</p>'
                    );
                });
            });

            // Ensure responsive behavior
            this.makeIframesResponsive($iframes);
        },

        // Make iframes responsive
        makeIframesResponsive: function($iframes) {
            $iframes.each(function() {
                var $iframe = $(this);

                // Set initial responsive attributes
                $iframe.css({
                    'width': '100%',
                    'border': 'none'
                });

                // Adjust height based on content (if possible)
                $iframe.on('load', function() {
                    try {
                        // Try to adjust height based on content
                        var iframeDoc = this.contentDocument || this.contentWindow.document;
                        if (iframeDoc) {
                            var height = iframeDoc.body.scrollHeight;
                            if (height > 200) { // Minimum height check
                                $iframe.css('height', height + 'px');
                            }
                        }
                    } catch (e) {
                        // Cross-origin restrictions prevent access, use default heights
                        Lindenhof.utils.log('Cannot access iframe content for height adjustment (cross-origin)', e);
                    }
                });
            });
        }
    };

    // Marquee functionality
    Lindenhof.marquee = {

        // Initialize marquee
        init: function($marquee) {
            var speed = $marquee.data('speed') || 'medium';
            var direction = $marquee.data('direction') || 'left';

            // Speed mapping
            var speedMap = {
                'slow': 60,
                'medium': 40,
                'fast': 20
            };

            var duration = speedMap[speed] || 40;
            var $track = $marquee.find('.images-marquee-block__marquee-track');

            if ($track.length && window.gsap) {
                // Calculate track width
                var trackWidth = $track[0].scrollWidth / 2; // Divide by 2 because we duplicated images

                // Create infinite loop animation
                var tl = gsap.timeline({ repeat: -1 });

                if (direction === 'left') {
                    tl.fromTo($track, {
                        x: 0
                    }, {
                        x: -trackWidth,
                        duration: duration,
                        ease: 'none'
                    });
                } else {
                    tl.fromTo($track, {
                        x: -trackWidth
                    }, {
                        x: 0,
                        duration: duration,
                        ease: 'none'
                    });
                }

                // Pause on hover
                $marquee.on('mouseenter', function() {
                    tl.pause();
                }).on('mouseleave', function() {
                    tl.resume();
                });
            }
        }
    };

    // Swup integration
    Lindenhof.swup = {

        // Initialize Swup hooks
        init: function() {
            if (!Lindenhof.config.swupEnabled) {
                Lindenhof.utils.log('Swup not enabled');
                return;
            }

            // Check if the main Swup instance exists (pageContainerWrap from main.js)
            if (typeof pageContainerWrap === 'undefined') {
                Lindenhof.utils.log('Main Swup instance (pageContainerWrap) not available');
                return;
            }

            Lindenhof.utils.log('Initializing Swup hooks for Lindenhof blocks');

            // Re-initialize blocks after page transition using the existing Swup instance
            pageContainerWrap.hooks.on('page:view', function() {
                Lindenhof.utils.log('Swup page view - reinitializing blocks');
                Lindenhof.blocks.init();
            });

            // Add page transition animations
            pageContainerWrap.hooks.on('animation:in:start', function() {
                if (window.gsap) {
                    gsap.from('#main', {
                        duration: 0.5,
                        y: 30,
                        opacity: 0,
                        ease: 'power2.out'
                    });
                }
            });
        }
    };

    // Main initialization
    Lindenhof.init = function() {
        Lindenhof.utils.log('Initializing Lindenhof');

        // Initialize Swup first
        Lindenhof.swup.init();

        // Initialize blocks
        Lindenhof.blocks.init();

        // Global event handlers
        $(window).on('resize', Lindenhof.utils.debounce(function() {
            Lindenhof.utils.log('Window resized - reinitializing marquees');
            $('.images-marquee-block__marquee').each(function() {
                Lindenhof.marquee.init($(this));
            });
        }, 250));

        Lindenhof.utils.log('Lindenhof initialization complete');
    };

    // Initialize when DOM is ready
    $(document).ready(function() {
        Lindenhof.init();
    });

})(jQuery);
