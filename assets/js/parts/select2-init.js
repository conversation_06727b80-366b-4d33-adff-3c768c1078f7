// Select2 initialization for contact forms
$(document).ready(function() {
    $(document).on("initPage", function() {
        initSelect2();
    });

    // Re-initialize Select2 after AJAX form submissions (Contact Form 7)
    document.addEventListener('wpcf7mailsent', function(event) {
        setTimeout(function() {
            initSelect2();
        }, 100);
    });

    // Re-initialize after form validation errors
    document.addEventListener('wpcf7invalid', function(event) {
        setTimeout(function() {
            initSelect2();
        }, 100);
    });
});

function initSelect2() {
    // Initialize Select2 on all select elements in contact forms
    const contactFormSelects = document.querySelectorAll('.wpcf7-form select.wpcf7-select');

    contactFormSelects.forEach(function(select) {
        if (typeof jQuery !== 'undefined' && typeof jQuery.fn.select2 !== 'undefined') {
            // Destroy existing Select2 instance if it exists
            if (jQuery(select).hasClass('select2-hidden-accessible')) {
                jQuery(select).select2('destroy');
            }

            // Initialize Select2
            jQuery(select).select2({
                minimumResultsForSearch: Infinity, // Disable search for simple dropdowns
                width: '100%',
                placeholder: select.getAttribute('data-placeholder') || 'Select an option...',
                allowClear: false
            });
        }
    });
}
