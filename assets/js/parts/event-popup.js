// Event Popup functionality with jQuery Cookie
$(document).ready(function() {
    $(document).on("initPage", function() {
        checkEventPopupVisibility();
    });
    
    // Also check immediately on first load
    checkEventPopupVisibility();
});

function closeEventPopup() {
    const popup = document.getElementById("eventPopup");
    if (popup) {
        popup.classList.add("hidden");
        // Store in cookie for 7 days using jQuery Cookie
        if (typeof jQuery !== 'undefined' && typeof jQuery.cookie !== 'undefined') {
            jQuery.cookie("hideEventPopup", "true", { expires: 7, path: "/" });
        }
    }
}

function checkEventPopupVisibility() {
    if (typeof jQuery !== 'undefined' && typeof jQuery.cookie !== 'undefined') {
        const hidePopup = jQuery.cookie("hideEventPopup");
        const popup = document.getElementById("eventPopup");
        
        if (hidePopup === "true" && popup) {
            popup.classList.add("hidden");
        }
    }
}

// Make functions globally available
window.closeEventPopup = closeEventPopup;
window.checkEventPopupVisibility = checkEventPopupVisibility;
