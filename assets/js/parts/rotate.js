$(document).ready(function(){
    $(document).off("initPage.rotate").on("initPage.rotate", function (){
        initRotators();
    });
});

function initRotators(){
    $('[data-rotate]').each(function (i, el) {

        // Snelheid en richting
        var speed = parseFloat($(el).attr('data-rotate-speed') || 10);
        if ($(window).width() <= 580) speed *= 1.5;
        else if ($(window).width() <= 1080) speed *= 1.2;

        var direction = $(el).attr('data-rotate-direction') === 'counter' ? -1 : 1;

        // Hoofdrotatie – draait altijd
        var rotationTimeline = gsap.to(el, {
            rotation: "+=" + (direction * 360),
            repeat: -1,
            duration: speed,
            ease: "linear"
        });

        // Scrollrichting beïnvloedt alleen de snelheid/richting
        ScrollTrigger.create({
            trigger: "body",
            start: "top top",
            end: "bottom bottom",
            onUpdate(self) {
                rotationTimeline.timeScale(self.direction === 1 ? 1 : -1);
            }
        });

        // Scroll speed effect (optioneel, maar timeline draait door)
        var scrollSpeed = $(el).attr('data-rotate-scroll-speed');
        if (scrollSpeed) {
            scrollSpeed = parseFloat(scrollSpeed);
            gsap.to(rotationTimeline, {
                timeScale: scrollSpeed,
                ease: "none",
                scrollTrigger: {
                    trigger: el,
                    start: "top bottom",
                    end: "bottom top",
                    scrub: 1
                }
            });
        }

        // Swipe functionaliteit (beïnvloedt alleen timeScale)
        if ($(el).attr('data-rotate-swipe')) {
            var hammertime = new Hammer($(el).get(0));
            hammertime.get('pan').set({ direction: Hammer.DIRECTION_HORIZONTAL });

            hammertime.on("panleft panright", function(ev) { 
                rotationTimeline.timeScale((ev.velocityX * 5) * -1);
            });

            hammertime.on("panend", function(ev) { 
                gsap.to(rotationTimeline, 0.6, { timeScale: direction });
            });
        }
    });
}

