$(document).ready(function(){
  $(document).on("initPage", function() {
    if ($('[data-slider]').length > 0) {
      resetSliders();
      initSliders();
    }
  });
});

var fillDotAnimation;
var clearDotAnimation; 
var noTimer = false;
var autoPlayInterval;

function initSliders() {

  fillDotAnimation;
  clearDotAnimation;
  noTimer = false;
  autoPlayInterval;

  $('[data-slider]').each(function(i, el) {
    var sliderWidth = $(el).width();
    var slideWidth = $(el).find('.slide:first-child').innerWidth();
    var slideLength = $(el).find('.slide').length;
    var slideInviewCounter = Math.floor(sliderWidth / slideWidth);

    if (slideLength > slideInviewCounter) {
      $(el).parent(".sliderWrapper").addClass("active");
      var slider;
      if ($(el).data("slider-loop")) {
        slider = $(el).flickity({
          cellAlign: 'left',
          prevNextButtons: false,
          pageDots: false,
          friction: 0.3,
          wrapAround: true,
          lazyLoad: true
        });
      } else {
        slider = $(el).flickity({
          contain: true,
          cellAlign: 'left',
          prevNextButtons: false,
          pageDots: false,
          friction: 0.3,
          lazyLoad: true
        });
      }

      if ($(el).data("timer")) {
        var time = $(el).data("timer");
        var currentIndex = $(el).find(".slide.is-selected").index();
        if (currentIndex === slideLength) {
          currentIndex = 0;
        }
        var currentDot = $(el).parents("section").find(".dot").eq(currentIndex);
        updateDotAnimations(currentDot, time);

        autoPlayInterval = setInterval(function() {
          currentIndex = $(el).find(".slide.is-selected").index();
          if (currentIndex === slideLength) {
            currentIndex = 0;
          }
          currentDot = $(el).parents("section").find(".dot").eq(currentIndex);
          slider.flickity('next');
          updateDotAnimations(currentDot, time);
        }, time);
      } else {
        noTimer = true;
      }

      // Event listeners voor slider navigatie
      $(el).parents("section").find('[data-slider-dot]').on('click', function(e) {
        var index = $(this).index();
        resetAutoPlay(slider, index);
        slider.flickity('select', index);
      });

      $(el).parent(".sliderWrapper").find('[data-prev], [data-next]').on('click', function() {
        resetAutoPlay(slider, $(slider).find(".slide.is-selected").index());
      });

      $(el).parent(".sliderWrapper").find('[data-prev]').on('click', function() {
        slider.flickity('previous');
      });

      $(el).parent(".sliderWrapper").find('[data-next]').on('click', function() {
        slider.flickity('next');
      });

      $(el).on('dragStart.flickity', function(event) {
        $(el).find("a").css("pointer-events", "none");
        if (!noTimer) {
          clearInterval(autoPlayInterval);
        }
      });

      $(el).on('dragEnd.flickity', function(event) {
        $(el).find("a").css("pointer-events", "inherit");
        resetAutoPlay(slider, slider.data('flickity').selectedIndex);
      });

      setSelectedSlides(el, 0, slider);

      slider.on('change.flickity', function() {
        resetAutoPlay(slider, slider.data('flickity').selectedIndex);
        setSelectedSlides(el, slider.data('flickity').selectedIndex, slider);
      });
    } else {
      $(el).parent(".sliderWrapper").removeClass("active");
    }
  });
}

function resetAutoPlay(slider, index) {
  if (!noTimer) {
    clearInterval(autoPlayInterval);
    var time = $(slider).data("timer");
    var currentIndex = index;
    var slideLength = $(slider).find('.slide').length;
    if (currentIndex === slideLength) {
      currentIndex = 0;
    }
    var currentDot = $(slider).parents("section").find(".dot").eq(index);
    updateDotAnimations(currentDot, time);

    autoPlayInterval = setInterval(function() {
      currentIndex = $(slider).find(".slide.is-selected").index();
      if (currentIndex === slideLength) {
        currentIndex = 0;
      }
      currentDot = $(slider).parents("section").find(".dot").eq(currentIndex);
      slider.flickity('next');
      updateDotAnimations(currentDot, time);
    }, time);
  }
}

function updateDotAnimations(currentDot, time) {
  clearDotAnimation && clearDotAnimation.kill();
  fillDotAnimation && fillDotAnimation.kill();
  var otherDots = $(currentDot).parents("section").find(".dot").not(currentDot).find(".innerDot");
  clearDotAnimation = gsap.to(otherDots, .3, { width: 0, ease: "power1.out" });
  fillDotAnimation = gsap.to($(currentDot).find(".innerDot"), time / 1000, { width: "100%", ease: "power1.inOut" });
}

function setSelectedSlides(slider, activeIndex, sliderObject) {
  var sliderWidth = $(slider).width();
  var slideWidth = $(slider).find('.slide').first().innerWidth();
  var slideLength = $(slider).find('.slide').length;
  var slideInviewCounter = $(slider).data("inview-items") || Math.floor(sliderWidth / slideWidth);
  var barWidth = (slideInviewCounter / slideLength) * 100;
  var leftPosition = (activeIndex / slideLength) * 100;

  $(slider).find(".slide.is-selected").removeClass("is-selected");
  var endReached = false;
  if (activeIndex + slideInviewCounter > slideLength) {
    endReached = true;
    sliderObject.data('flickity').selectedIndex = slideLength - slideInviewCounter;
  }
  $(slider).find(".slide").each(function(i, el) {
    if (i >= activeIndex && i < activeIndex + slideInviewCounter && !endReached) {
      $(el).addClass("is-selected");
      if ($(el).hasClass("is-selected")) {
        changeSliderIndicator(slideInviewCounter, slideLength, activeIndex, slider, barWidth, leftPosition);
      }
    } else if (endReached) {
      if (i >= slideLength - slideInviewCounter) {
        $(el).addClass("is-selected");
        if ($(el).hasClass("is-selected")) {
          changeSliderIndicator(slideInviewCounter, slideLength, activeIndex, slider, barWidth, leftPosition);
        }
      }
    }
    if ($(el).hasClass("is-selected")) {
      gsap.to($(el), { opacity: 1, duration: 0.3, ease: "power2.out" });
    } else {
      gsap.to($(el), { opacity: 0.2, duration: 0.3, ease: "power2.out" });
    }
  });
  $(slider).parent(".sliderWrapper").find('[data-prev]').toggleClass("disabled", activeIndex === 0);
  $(slider).parent(".sliderWrapper").find('[data-next]').toggleClass("disabled", activeIndex + slideInviewCounter >= slideLength);
}

function resetSliders() {
  if (autoPlayInterval) {
    clearInterval(autoPlayInterval);
  }

  $('[data-slider]').each(function(i, el) {
    if ($(el).data("flickity")) {
      $(el).flickity('destroy');
    }
  });

  clearDotAnimation && clearDotAnimation.kill();
  fillDotAnimation && fillDotAnimation.kill();
}

function changeSliderIndicator(slideInviewCounter, slideLength, activeIndex, slider, barWidth, leftPosition) {
  var barWidth = (slideInviewCounter / slideLength) * 100;
  var leftPosition = (activeIndex / slideLength) * 100;
  gsap.to($(slider).parents(".sliderWrapper").find('.sliderIndicator .innerBar'), {
    width: barWidth + '%',
    left: leftPosition + '%',
    duration: .7,
    ease: "power2.out"
  });
}
