<?php get_header(); ?>

<section class="newsArticle" data-init>
  <div class="contentWrapper smallest">
    <article class="articleContent">

      <!-- Article Date (Subtitle) -->
      <div class="textTitle primary smaller">
        <?php echo get_the_date('j F Y'); ?>
      </div>

      <!-- Article Title -->
      <h1 class="bigTitle" data-lines data-words>
        <?php the_title(); ?>
      </h1>

      <!-- Social Sharing Links -->
      <div class="socialSharing">
        <?php
        $current_url = urlencode(get_permalink());
        $current_title = urlencode(get_the_title());
        ?>

        <?php if (get_theme_mod('customTheme-main-callout-facebook')) : ?>
          <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo $current_url; ?>"
             title="Deel op Facebook"
             target="_blank"
             class="socialShareLink">
            <i class="icon-facebook"></i>
          </a>
        <?php endif; ?>

        <?php if (get_theme_mod('customTheme-main-callout-linkedin')) : ?>
          <a href="https://www.linkedin.com/sharing/share-offsite/?url=<?php echo $current_url; ?>"
             title="Deel op LinkedIn"
             target="_blank"
             class="socialShareLink">
            <i class="icon-linkedin"></i>
          </a>
        <?php endif; ?>
      </div>

      <!-- Article Content -->
      <div class="articleBody text">
        <?php the_content(); ?>
      </div>
    </article>
  </div>
</section>

<!-- latest news block with related blog articles (without current one) -->
 <?php

$news = get_posts(array(
  'post_type' => 'post',
  'posts_per_page' => 6,
  'orderby' => 'date',
  'order' => 'DESC',
  'post__not_in' => array(get_the_ID())
));
?>
<section class="giftLatestNews <?= esc_attr(gift631_get_block_classes()) ?>" data-init>
  <div class="contentWrapper">
    <div class="sliderWrapper">
      <div class="slider" data-slider data-loop-slider="true">
        <?php foreach ($news as $n): ?>
          <div class="slide">
            <a href="<?= esc_url(get_permalink($n)) ?>" class="news">
              <div class="imageWrapper">
                <div class="innerImage">
                  <img class="lazy" data-src="<?= esc_url(get_the_post_thumbnail_url($n, 'medium_large')) ?>" alt="<?= esc_attr(get_the_title($n)) ?>">
                </div>
              </div>
              <div class="info">
                <div class="textTitle"><?= esc_html(get_the_date('', $n)) ?></div>
                <h3 class="normalTitle"><?= esc_html(get_the_title($n)) ?></h3>
              </div>
            </a>
          </div>
        <?php endforeach; ?>
      </div>
      <div class="sliderIndicator">
        <div class="innerBar"></div>
      </div>
      <div class="sliderButton arrowButton prev" data-prev><i class="icon-arrow-left"></i><i class="icon-arrow-left"></i></div>
      <div class="sliderButton arrowButton next" data-next><i class="icon-arrow-right"></i><i class="icon-arrow-right"></i></div>
    </div>
  </div>
</section>

<?php get_footer(); ?>
