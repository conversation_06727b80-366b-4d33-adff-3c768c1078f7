<!DOCTYPE html>
<html lang="nl" dir="ltr">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <title><?php echo get_theme_mod('customTheme-main-callout-title') ? get_theme_mod('customTheme-main-callout-title') : get_bloginfo('name'); ?> | <?php the_title(); ?></title>
  <meta name="robots" content="follow, index, max-snippet:-1, max-video-preview:-1, max-image-preview:large">
  <meta name="msapplication-TileColor" content="#00aba9">
  <meta name="theme-color" content="#ffffff">
  <meta name="description" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-description')); ?>">
  <meta name="author" content="https://www.linkedin.com/in/dennisthemenace/"/>
  <meta property="og:title" content="<?php echo get_theme_mod('customTheme-main-callout-title') ? get_theme_mod('customTheme-main-callout-title') : get_bloginfo('name'); ?> | <?php the_title(); ?>" />
  <meta property="og:description" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-description')); ?>" />
  <meta property="og:image" content="<?php echo esc_url(wp_get_attachment_url(get_theme_mod('customTheme-main-callout-featured-image'))); ?>" />
  <meta property="og:image:alt" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-title')); ?>" />
  <meta property="og:image:width" content="1200" />
  <meta property="og:image:height" content="630" />
  <meta property="og:image:type" content="image/jpeg" />
  <meta property="og:type" content="website" />
  <meta property="og:url" content="<?php echo esc_url(get_permalink()); ?>" />
  <meta property="og:site_name" content="<?php echo get_theme_mod('customTheme-main-callout-title') ? get_theme_mod('customTheme-main-callout-title') : get_bloginfo('name'); ?>" />
  <meta property="og:locale" content="nl" />
  <meta name="DC.title" content="<?php echo the_title(); ?>">
  <meta name="DC.creator" content="Door Dennis">
  <meta name="DC.subject" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-description')); ?>">
  <meta name="DC.description" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-description')); ?>">
  <meta name="DC.publisher" content="<?php echo get_bloginfo('name'); ?>">
  <meta name="DC.language" content="nl">
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="<?php echo get_theme_mod('customTheme-main-callout-title') ? get_theme_mod('customTheme-main-callout-title') : get_bloginfo('name'); ?> | <?php the_title(); ?>">
  <meta name="twitter:description" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-description')); ?>">
  <meta name="twitter:image" content="<?php echo esc_url(wp_get_attachment_url(get_theme_mod('customTheme-main-callout-featured-image'))); ?>">
  <link rel="canonical" href="<?php echo esc_url(get_permalink()); ?>" />
  <link rel="icon" href="<?php echo get_stylesheet_directory_uri(); ?>/favicon.ico" type="image/x-icon">
  <link rel="shortcut icon" href="<?php echo get_stylesheet_directory_uri(); ?>/favicon.ico" type="image/x-icon">
  <link rel="stylesheet" href="https://use.typekit.net/sui0kvj.css">
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "<?php echo get_bloginfo('name'); ?>",
    "url": "<?php echo esc_url(home_url()); ?>",
    "logo": "<?php echo get_stylesheet_directory_uri(); ?>/logo.png",
    "description": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-description')); ?>",
    "image": "<?php echo esc_url(get_theme_mod('customTheme-main-callout-featured-image')); ?>",
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-telephone')); ?>",
      "contactType": "customer service",
      "contactOption": "TollFree",
      "areaServed": "NL",
      "availableLanguage": "Dutch"
    },
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-address')); ?>"
    },
    "sameAs": [
      "<?php echo esc_url(get_theme_mod('customTheme-main-callout-facebook')); ?>",
      "<?php echo esc_url(get_theme_mod('customTheme-main-callout-linkedin')); ?>",
      "<?php echo esc_url(get_theme_mod('customTheme-main-callout-tiktok')); ?>",
      "<?php echo esc_url(get_theme_mod('customTheme-main-callout-instagram')); ?>"
    ],
    "email": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-mail')); ?>",
    "telephone": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-telephone')); ?>",
    "additionalProperty": [
      {
        "@type": "PropertyValue",
        "name": "Company Information",
        "value": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-company-information')); ?>"
      },
      {
        "@type": "PropertyValue",
        "name": "Analytics ID",
        "value": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-analytics')); ?>"
      }
    ]
  }
  </script>

  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', '<?php echo get_theme_mod('customTheme-main-callout-analytics') ?>', {
      'anonymize_ip': true
    });
  </script>

  <?php wp_head(); ?>
</head>
<?php if(get_theme_mod("customTheme-main-callout-analytics")) { ?>
  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=<?php echo get_theme_mod('customTheme-main-callout-analytics') ?>"></script>
  
<?php } ?>
  <body class="no-scroll">
    <!-- <header>
      <div class="background"></div>
      <div class="contentWrapper">
        <div class="col" id="headerContainer">
          <div class="innerMenu">
              <?php wp_nav_menu( array(
                  'theme_location' => 'primary-menu',
              ) ); ?>
          </div>
        </div>
        <div class="col">
            <a href="/" title="Logo | <?php echo get_theme_mod('customTheme-main-callout-title') ?>" class="logo">
            </a>
        </div>
        <div class="col">
          <div id="burger" class="hamburger"><div class="innerBurger"><span class="border"></span><span class="border"></span><span class="border"></span></div></div>
          <?php
            $button = get_field('header_button', 'option');
            $shop_link = get_field('shop_link', 'option'); // external URL
          ?>
          <?php if ($shop_link): ?>
            <a class="button" title="ONLINE STORE" href="<?= esc_url($shop_link) ?>" target="_blank" data-no-swup="true">
              <span class="hiddenText">ONLINE STORE</span>
              <span class="innerTexts">
                <span class="innerText" aria-hidden="true" data-words>ONLINE STORE</span>
                <span class="innerText" aria-hidden="true" data-words>ONLINE STORE</span>
              </span>
            </a>
          <?php endif; ?>
          <?php if ($button): ?>
            <?php render_button_from_array($button, "primary"); ?>
          <?php endif; ?>
        </div>
      </div>
    </header>
    <div id="menu" class="mainMenu" data-lenis-prevent="true">
      <div class="background"></div>
      <div id="menuContainer" class="innerContent">
        <?php wp_nav_menu( array(
            'theme_location' => 'footer-menu-1',
        ) ); ?>
        <div class="socials">
           <?php if ($shop_link): ?>
            <a class="button primary" title="ONLINE STORE" href="<?= esc_url($shop_link) ?>" target="_blank" data-no-swup="true">
              <span class="hiddenText">ONLINE STORE</span>
              <span class="innerTexts">
                <span class="innerText" aria-hidden="true" data-words>ONLINE STORE</span>
                <span class="innerText" aria-hidden="true" data-words>ONLINE STORE</span>
              </span>
            </a>
          <?php endif; ?>
          <?php if (get_theme_mod('customTheme-main-callout-facebook')) : ?>
            <a href="<?php echo esc_url(get_theme_mod('customTheme-main-callout-facebook')); ?>" title="Facebook" target="_blank" class="socialLink">
              <i class="icon-facebook"></i>
            </a>
          <?php endif; ?>
          <?php if (get_theme_mod('customTheme-main-callout-linkedin')) : ?>
            <a href="<?php echo esc_url(get_theme_mod('customTheme-main-callout-linkedin')); ?>" title="LinkedIn" target="_blank" class="socialLink">
              <i class="icon-linkedin"></i>
            </a>
          <?php endif; ?> 
          <?php if (get_theme_mod('customTheme-main-callout-tiktok')) : ?>
            <a href="<?php echo esc_url(get_theme_mod('customTheme-main-callout-tiktok')); ?>" title="Tiktok" target="_blank" class="socialLink">
              <i class="icon-tiktok"></i>
            </a>
          <?php endif; ?>
          <?php if (get_theme_mod('customTheme-main-callout-instagram')) : ?>
            <a href="<?php echo esc_url(get_theme_mod('customTheme-main-callout-instagram')); ?>" title="Instagram" target="_blank" class="socialLink">
              <i class="icon-instagram"></i>
            </a>
          <?php endif; ?>
        </div>
      </div>
    </div> -->
  <div id="pageContainer" class="transition-fade blocks">
   <div class="innerBlocks">