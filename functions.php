<?php
/**
 * Lindenhof Theme Functions
 *
 * @package Lindenhof
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

function jn_enqueue_assets() {
    // Critical scripts that need to load immediately
    $critical_scripts = array(
        'Jquery' => '/libs/jquery.min.js',
        'GSAP' => '/libs/gsap.min.js',
        'main_js' => '/assets/js/main.js',
    );

    // Non-critical scripts that can be deferred
    $deferred_scripts = array(
        'Lenis' => '/libs/lenis.min.js',
        'Swup' => '/libs/swup.js',
        'Swup_head' => '/libs/swup_head.js',
        'Swup_Gtag' => '/libs/swup_gtag.js',
        'Select2' => '/libs/select2.min.js',
        'HAMMER' => '/libs/hammer.min.js',
        'ScrollTrigger' => '/libs/ScrollTrigger.min.js',
        'Custom_Ease' => '/libs/CustomEase.min.js',
        'FLICKITY' => '/libs/flickity.min.js',
        'SPLITTEXT' => '/libs/SplitText.min.js',
        'Header' => '/assets/js/header.js?7492',
        'Footer' => '/assets/js/footer.js',
        'Menu' => '/assets/js/parts/menu.js',
        'Parallax' => '/assets/js/parts/parallax.js',
        'Gallery' => '/assets/js/parts/gallery.js',
        'Cursor' => '/assets/js/parts/cursor.js',
        'Slider' => '/assets/js/parts/slider.js',
        'Split' => '/assets/js/parts/split.js',
        'Marquee' => '/assets/js/parts/marquee.js',
        'Rotate' => '/assets/js/parts/rotate.js',
        'CookieBanner' => '/assets/js/parts/cookiebanner.js',
    );

    // Enqueue critical scripts first (blocking)
    foreach ($critical_scripts as $handle => $path) {
        wp_enqueue_script($handle, get_theme_file_uri($path), array(), '1.0', false); // Load in head
    }

    // Enqueue deferred scripts (non-blocking)
    foreach ($deferred_scripts as $handle => $path) {
        wp_enqueue_script($handle, get_theme_file_uri($path), array(), '1.0', true); // Load in footer
    }

    wp_enqueue_style('main', get_stylesheet_uri());
    wp_enqueue_style('select2', get_theme_file_uri('/libs/select2.min.css'), array(), '1.1', 'all');
    wp_enqueue_style('cookiebanner', get_theme_file_uri('/assets/css/cookiebanner.css'), array(), '1.0', 'all');
}

add_action('wp_enqueue_scripts', 'jn_enqueue_assets');

// Theme setup
add_action('after_setup_theme', 'lindenhof_theme_setup');
function lindenhof_theme_setup() {
    // Add theme support
    add_theme_support('post-thumbnails');
    add_theme_support('title-tag');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ));

    // Register navigation menus
    register_nav_menus(array(
        'primary' => esc_html__('Primary Menu', 'lindenhof'),
        'footer' => esc_html__('Footer Menu', 'lindenhof'),
    ));
}

// Check if ACF Pro is active
add_action('admin_notices', 'lindenhof_check_acf_pro');
function lindenhof_check_acf_pro() {
    if (!function_exists('acf_register_block_type') || !function_exists('acf_add_local_field_group')) {
        echo '<div class="notice notice-error"><p>';
        echo '<strong>Lindenhof Theme:</strong> Advanced Custom Fields Pro is vereist voor dit thema. ';
        echo '<a href="' . admin_url('plugins.php') . '">Installeer en activeer ACF Pro</a>';
        echo '</p></div>';
    }
}

// Include required files
require_once get_template_directory() . '/inc/helpers.php';
require_once get_template_directory() . '/inc/custom-post-types.php';
require_once get_template_directory() . '/inc/blocks/register-blocks.php';
require_once get_template_directory() . '/inc/huurkalender-proxy.php';
require_once get_template_directory() . '/inc/admin-settings.php';

// Enqueue scripts and styles (frontend only)
add_action('wp_enqueue_scripts', 'lindenhof_enqueue_assets');
function lindenhof_enqueue_assets() {
    // Only load on frontend, not in admin
    if (is_admin()) {
        return;
    }

    // Main stylesheet
    wp_enqueue_style('lindenhof-style', get_stylesheet_uri(), array(), '1.0.0');

    // jQuery (WordPress core)
    wp_enqueue_script('jquery');

    // Only enqueue JavaScript if files exist
    $js_file = get_template_directory() . '/assets/js/lindenhof-blocks.js';
    if (file_exists($js_file)) {
        // Lindenhof blocks JavaScript - depends on jQuery and main.js (which includes Swup)
        wp_enqueue_script(
            'lindenhof-blocks',
            get_theme_file_uri('/assets/js/lindenhof-blocks.js'),
            array('jquery', 'main_js'),
            '1.0.0',
            true
        );

        // Localize script for AJAX
        wp_localize_script('lindenhof-blocks', 'lindenhof_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('lindenhof_nonce'),
            'rest_url' => rest_url('lindenhof/v1/'),
        ));
    }

    // Optional: Load GSAP and Swup from CDN if needed
    // wp_enqueue_script('gsap', 'https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js', array(), '3.12.2', true);
    // wp_enqueue_script('swup', 'https://unpkg.com/swup@3/dist/swup.min.js', array(), '3.1.1', true);
}

// ACF Local JSON setup
add_filter('acf/settings/save_json', 'lindenhof_acf_json_save_point');
function lindenhof_acf_json_save_point($path) {
    return get_stylesheet_directory() . '/acf-json';
}

add_filter('acf/settings/load_json', 'lindenhof_acf_json_load_point');
function lindenhof_acf_json_load_point($paths) {
    unset($paths[0]);
    $paths[] = get_stylesheet_directory() . '/acf-json';
    return $paths;
}

// Clean up WordPress head
add_action('init', 'lindenhof_cleanup_wp_head');
function lindenhof_cleanup_wp_head() {
    // Remove emoji scripts
    remove_action('wp_head', 'print_emoji_detection_script', 7);
    remove_action('wp_print_styles', 'print_emoji_styles');

    // Remove WordPress version
    remove_action('wp_head', 'wp_generator');

    // Remove RSD link
    remove_action('wp_head', 'rsd_link');

    // Remove wlwmanifest link
    remove_action('wp_head', 'wlwmanifest_link');

    // Remove shortlink
    remove_action('wp_head', 'wp_shortlink_wp_head');
}



// Add favicon
add_action('wp_head', 'lindenhof_favicon');
function lindenhof_favicon() {
    echo '<link rel="shortcut icon" href="' . esc_url(get_stylesheet_directory_uri() . '/favicon.ico') . '">' . "\n";
}

