<?php
/**
 * Default Content for Lindenhof Blocks
 * 
 * @package Lindenhof
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Set default values for faciliteiten block when creating new posts/pages
 */
add_action('acf/save_post', 'lindenhof_set_default_faciliteiten_values', 20);
function lindenhof_set_default_faciliteiten_values($post_id) {
    // Only run on frontend or when creating new posts
    if (is_admin() && !wp_doing_ajax()) {
        return;
    }
    
    // Check if this is a new post/page
    $post = get_post($post_id);
    if (!$post || $post->post_status !== 'auto-draft') {
        return;
    }
    
    // Check if faciliteiten block exists in content
    $content = get_post_field('post_content', $post_id);
    if (strpos($content, 'acf/faciliteiten-block') === false) {
        return;
    }
    
    // Set default values for Lindenhof accommodatie
    $default_values = array(
        // Doelgroepen - mostly yes for family-friendly accommodation
        'doelgroep_familie' => true,
        'doelgroep_vrienden_boven_30' => true,
        'doelgroep_studenten' => false,
        'doelgroep_feestgroep' => false,
        'doelgroep_jongeren_onder_25' => false,
        'doelgroep_school_jeugdkamp' => true,
        'doelgroep_sportvereniging' => true,
        'doelgroep_voetbalteam' => true,
        'doelgroep_vrienden_onder_30' => false,
        
        // Algemeen - standard facilities
        'algemeen_wifi' => true,
        'algemeen_douches' => true,
        'algemeen_toiletten' => true,
        'algemeen_wastafels' => true,
        'algemeen_kinderbedjes' => true,
        'algemeen_kinderstoelen' => true,
        'algemeen_traphekjes' => true,
        
        // Faciliteiten - full kitchen and living facilities
        'faciliteiten_woonkamer' => true,
        'faciliteiten_televisie' => true,
        'faciliteiten_keuken' => true,
        'faciliteiten_gaspitten' => true,
        'faciliteiten_koelkast' => true,
        'faciliteiten_diepvries' => true,
        'faciliteiten_magnetron' => true,
        'faciliteiten_oven' => true,
        'faciliteiten_vaatwasser' => true,
    );
    
    // Only set values if they don't already exist
    foreach ($default_values as $field_name => $default_value) {
        $existing_value = get_field($field_name, $post_id);
        if ($existing_value === null || $existing_value === '') {
            update_field($field_name, $default_value, $post_id);
        }
    }
}

/**
 * Set default accommodatie settings when theme is activated
 */
add_action('after_switch_theme', 'lindenhof_set_default_accommodatie_settings');
function lindenhof_set_default_accommodatie_settings() {
    // Set default accommodatie information if not already set
    $defaults = array(
        'lindenhof_accommodatie_locatie' => 'Geijsteren, Nederland',
        'lindenhof_accommodatie_personen' => '20',
        'lindenhof_accommodatie_slaapplaatsen' => '16',
    );
    
    foreach ($defaults as $option_name => $default_value) {
        $existing_value = get_option($option_name);
        if (empty($existing_value)) {
            update_option($option_name, $default_value);
        }
    }
}

/**
 * Add admin notice to configure Google API key
 */
add_action('admin_notices', 'lindenhof_google_api_notice');
function lindenhof_google_api_notice() {
    // Only show to administrators
    if (!current_user_can('manage_options')) {
        return;
    }
    
    // Check if Google API key is configured
    $google_api_key = lindenhof_get_setting('google_api_key');
    if (!empty($google_api_key)) {
        return;
    }
    
    // Only show on relevant pages
    $screen = get_current_screen();
    if (!$screen || !in_array($screen->id, array('dashboard', 'edit-page', 'page', 'settings_page_lindenhof-settings'))) {
        return;
    }
    
    ?>
    <div class="notice notice-warning is-dismissible">
        <p>
            <strong><?php echo esc_html__('Lindenhof Theme:', 'lindenhof'); ?></strong>
            <?php echo esc_html__('Configureer je Google API key om Google Reviews te tonen.', 'lindenhof'); ?>
            <a href="<?php echo admin_url('admin.php?page=lindenhof-settings'); ?>" class="button button-small">
                <?php echo esc_html__('Instellingen', 'lindenhof'); ?>
            </a>
        </p>
    </div>
    <?php
}

/**
 * Create default content when theme is activated
 */
add_action('after_switch_theme', 'lindenhof_create_default_content');
function lindenhof_create_default_content() {
    // Check if we already have a homepage
    $homepage = get_option('page_on_front');
    if ($homepage && get_post($homepage)) {
        return; // Homepage already exists
    }
    
    // Create default homepage with intro block
    $homepage_content = '<!-- wp:acf/intro-block {"id":"block_intro","name":"acf/intro-block","data":{"title":"Welkom bij Lindenhof","subtitle":"Uw perfecte vakantiebestemming in Geijsteren","content":"Ontdek de rust en ruimte van ons prachtige vakantiehuis, ideaal voor families en groepen tot 20 personen.","show_google_reviews":"1","show_faciliteiten":"1","show_sticky_booking":"1"},"mode":"preview"} /-->

<!-- wp:acf/faciliteiten-block {"id":"block_faciliteiten","name":"acf/faciliteiten-block","data":{"title":"Faciliteiten"},"mode":"preview"} /-->

<!-- wp:acf/eten-drinken-block {"id":"block_eten_drinken","name":"acf/eten-drinken-block","data":{"title":"Eten & Drinken","content":"Ontdek de lokale culinaire hoogstandjes in de omgeving van Geijsteren."},"mode":"preview"} /-->

<!-- wp:acf/omgeving-block {"id":"block_omgeving","name":"acf/omgeving-block","data":{"title":"Omgeving","content":"Verken de prachtige natuur en bezienswaardigheden rondom Lindenhof."},"mode":"preview"} /-->

<!-- wp:acf/contact-block {"id":"block_contact","name":"acf/contact-block","data":{"title":"Contact","show_map":"1"},"mode":"preview"} /-->';
    
    $homepage_id = wp_insert_post(array(
        'post_title' => 'Lindenhof - Vakantiehuis Geijsteren',
        'post_content' => $homepage_content,
        'post_status' => 'publish',
        'post_type' => 'page',
        'post_name' => 'home',
    ));
    
    if ($homepage_id && !is_wp_error($homepage_id)) {
        // Set as homepage
        update_option('show_on_front', 'page');
        update_option('page_on_front', $homepage_id);
        
        // Set default ACF values for intro block
        update_field('title', 'Welkom bij Lindenhof', $homepage_id);
        update_field('subtitle', 'Uw perfecte vakantiebestemming in Geijsteren', $homepage_id);
        update_field('content', 'Ontdek de rust en ruimte van ons prachtige vakantiehuis, ideaal voor families en groepen tot 20 personen.', $homepage_id);
        update_field('show_google_reviews', true, $homepage_id);
        update_field('show_faciliteiten', true, $homepage_id);
        update_field('show_sticky_booking', true, $homepage_id);
        update_field('booking_widget_type', 'huurkalender', $homepage_id);
    }
}

/**
 * Add sample data for eten & drinken and omgeving blocks
 */
add_action('acf/save_post', 'lindenhof_add_sample_content', 25);
function lindenhof_add_sample_content($post_id) {
    // Only run once when creating new content
    if (get_post_meta($post_id, '_lindenhof_sample_added', true)) {
        return;
    }
    
    $post = get_post($post_id);
    if (!$post || $post->post_status === 'auto-draft') {
        return;
    }
    
    $content = get_post_field('post_content', $post_id);
    
    // Add sample restaurants for eten-drinken block
    if (strpos($content, 'acf/eten-drinken-block') !== false) {
        $sample_restaurants = array(
            array(
                'name' => 'Restaurant De Maaspoort',
                'description' => 'Gezellig restaurant met lokale specialiteiten',
                'distance' => '2 km',
                'link' => 'https://example.com'
            ),
            array(
                'name' => 'Café Het Dorpsplein',
                'description' => 'Traditioneel café in het centrum',
                'distance' => '1.5 km',
                'link' => ''
            )
        );
        
        if (!get_field('restaurants', $post_id)) {
            update_field('restaurants', $sample_restaurants, $post_id);
        }
    }
    
    // Add sample activities for omgeving block
    if (strpos($content, 'acf/omgeving-block') !== false) {
        $sample_activities = array(
            array(
                'name' => 'Nationaal Park De Maasduinen',
                'description' => 'Prachtig natuurgebied voor wandelen en fietsen',
                'distance' => '5 km',
                'link' => 'https://example.com'
            ),
            array(
                'name' => 'Kasteel Arcen',
                'description' => 'Historisch kasteel met prachtige tuinen',
                'distance' => '8 km',
                'link' => ''
            )
        );
        
        if (!get_field('activities', $post_id)) {
            update_field('activities', $sample_activities, $post_id);
        }
    }
    
    // Mark as processed
    update_post_meta($post_id, '_lindenhof_sample_added', true);
}

/**
 * Helper function to get default facility value
 */
function lindenhof_get_default_facility_value($facility_name) {
    $defaults = array(
        // Doelgroepen
        'doelgroep_familie' => true,
        'doelgroep_vrienden_boven_30' => true,
        'doelgroep_studenten' => false,
        'doelgroep_feestgroep' => false,
        'doelgroep_jongeren_onder_25' => false,
        'doelgroep_school_jeugdkamp' => true,
        'doelgroep_sportvereniging' => true,
        'doelgroep_voetbalteam' => true,
        'doelgroep_vrienden_onder_30' => false,
        
        // Algemeen
        'algemeen_wifi' => true,
        'algemeen_douches' => true,
        'algemeen_toiletten' => true,
        'algemeen_wastafels' => true,
        'algemeen_kinderbedjes' => true,
        'algemeen_kinderstoelen' => true,
        'algemeen_traphekjes' => true,
        
        // Faciliteiten
        'faciliteiten_woonkamer' => true,
        'faciliteiten_televisie' => true,
        'faciliteiten_keuken' => true,
        'faciliteiten_gaspitten' => true,
        'faciliteiten_koelkast' => true,
        'faciliteiten_diepvries' => true,
        'faciliteiten_magnetron' => true,
        'faciliteiten_oven' => true,
        'faciliteiten_vaatwasser' => true,
    );
    
    return isset($defaults[$facility_name]) ? $defaults[$facility_name] : false;
}
