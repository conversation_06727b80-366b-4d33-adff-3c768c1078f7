<?php
/**
 * Lindenhof Block Registration
 * 
 * @package Lindenhof
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Register block category
add_filter('block_categories_all', 'lindenhof_block_categories', 10, 2);
function lindenhof_block_categories($categories, $post) {
    return array_merge(
        $categories,
        array(
            array(
                'slug' => 'lindenhof-blocks',
                'title' => __('Lindenhof Blocks', 'lindenhof'),
                'icon' => 'building',
            ),
        )
    );
}

// Register all Lindenhof blocks
add_action('acf/init', 'lindenhof_register_blocks');
function lindenhof_register_blocks() {
    // Check if ACF function exists
    if (!function_exists('acf_register_block_type')) {
        return;
    }

    // Define all Lindenhof blocks
    $blocks = array(
        'intro-block' => array(
            'title' => __('Intro Block', 'lindenhof'),
            'description' => __('Introductie sectie met titel en tekst', 'lindenhof'),
            'icon' => 'welcome-learn-more',
            'keywords' => array('intro', 'introduction', 'welcome'),
        ),
        'faciliteiten-block' => array(
            'title' => __('Faciliteiten Block', 'lindenhof'),
            'description' => __('Overzicht van faciliteiten en voorzieningen', 'lindenhof'),
            'icon' => 'admin-home',
            'keywords' => array('facilities', 'amenities', 'features'),
        ),
        'algemeen-block' => array(
            'title' => __('Algemeen Block', 'lindenhof'),
            'description' => __('Algemene informatie sectie', 'lindenhof'),
            'icon' => 'info',
            'keywords' => array('general', 'information', 'about'),
        ),
        'eten-drinken-block' => array(
            'title' => __('Eten & Drinken Block', 'lindenhof'),
            'description' => __('Informatie over eten en drinken', 'lindenhof'),
            'icon' => 'food',
            'keywords' => array('food', 'drink', 'dining', 'restaurant'),
        ),
        'omgeving-block' => array(
            'title' => __('Omgeving Block', 'lindenhof'),
            'description' => __('Informatie over de omgeving en activiteiten', 'lindenhof'),
            'icon' => 'location-alt',
            'keywords' => array('environment', 'surroundings', 'activities'),
        ),
        'huurkalender-block' => array(
            'title' => __('Huurkalender Block', 'lindenhof'),
            'description' => __('Boekingswidget met beschikbaarheid', 'lindenhof'),
            'icon' => 'calendar-alt',
            'keywords' => array('booking', 'calendar', 'availability', 'reservation'),
        ),
        'images-marquee-block' => array(
            'title' => __('Images Marquee Block', 'lindenhof'),
            'description' => __('Scrollende galerij van afbeeldingen', 'lindenhof'),
            'icon' => 'images-alt2',
            'keywords' => array('gallery', 'images', 'marquee', 'slider'),
        ),
        'contact-block' => array(
            'title' => __('Contact Block', 'lindenhof'),
            'description' => __('Contact informatie en formulier', 'lindenhof'),
            'icon' => 'email-alt',
            'keywords' => array('contact', 'form', 'email', 'phone'),
        ),
    );

    // Register each block with ACF
    foreach ($blocks as $block_name => $block_config) {
        acf_register_block_type(array(
            'name' => $block_name,
            'title' => $block_config['title'],
            'description' => $block_config['description'],
            'icon' => $block_config['icon'],
            'category' => 'lindenhof-blocks',
            'keywords' => $block_config['keywords'],
            'supports' => array(
                'anchor' => true,
                'align' => false,
                'html' => false,
                'jsx' => true,
            ),
            'render_template' => get_template_directory() . '/template-parts/blocks/' . $block_name . '.php',
        ));
    }
}

// This function is no longer needed as ACF handles rendering via render_template

// Include ACF field group registrations
if (file_exists(get_template_directory() . '/inc/blocks/acf-field-groups.php')) {
    require_once get_template_directory() . '/inc/blocks/acf-field-groups.php';
}

// Block editor assets - removed to prevent frontend styling in backend

// Add custom block patterns
add_action('init', 'lindenhof_register_block_patterns');
function lindenhof_register_block_patterns() {
    // Register pattern category
    register_block_pattern_category(
        'lindenhof-patterns',
        array('label' => __('Lindenhof Patronen', 'lindenhof'))
    );
    
    // Homepage pattern
    register_block_pattern(
        'lindenhof/homepage-layout',
        array(
            'title' => __('Homepage Layout', 'lindenhof'),
            'description' => __('Standaard homepage layout voor Lindenhof', 'lindenhof'),
            'content' => '<!-- wp:acf/intro-block /-->
                         <!-- wp:acf/faciliteiten-block /-->
                         <!-- wp:acf/images-marquee-block /-->
                         <!-- wp:acf/huurkalender-block /-->
                         <!-- wp:acf/contact-block /-->',
            'categories' => array('lindenhof-patterns'),
        )
    );
    
    // About page pattern
    register_block_pattern(
        'lindenhof/about-layout',
        array(
            'title' => __('Over Ons Layout', 'lindenhof'),
            'description' => __('Standaard over ons layout voor Lindenhof', 'lindenhof'),
            'content' => '<!-- wp:acf/intro-block /-->
                         <!-- wp:acf/algemeen-block /-->
                         <!-- wp:acf/omgeving-block /-->
                         <!-- wp:acf/images-marquee-block /-->',
            'categories' => array('lindenhof-patterns'),
        )
    );
}

// Add block styles
add_action('init', 'lindenhof_register_block_styles');
function lindenhof_register_block_styles() {
    // Add custom styles for core blocks if needed
    register_block_style(
        'core/paragraph',
        array(
            'name' => 'lindenhof-large-text',
            'label' => __('Grote tekst', 'lindenhof'),
        )
    );
    
    register_block_style(
        'core/heading',
        array(
            'name' => 'lindenhof-accent-heading',
            'label' => __('Accent kop', 'lindenhof'),
        )
    );
}

// Restrict to only Lindenhof custom blocks
add_filter('allowed_block_types_all', 'lindenhof_allowed_block_types', 10, 2);
function lindenhof_allowed_block_types($allowed_blocks, $editor_context) {
    // Only allow Lindenhof custom blocks
    $lindenhof_blocks = array(
        'acf/intro-block',
        'acf/faciliteiten-block',
        'acf/algemeen-block',
        'acf/eten-drinken-block',
        'acf/omgeving-block',
        'acf/huurkalender-block',
        'acf/images-marquee-block',
        'acf/contact-block',
    );

    // Optionally allow some core blocks for basic formatting
    $allowed_core_blocks = array(
        'core/paragraph',
        'core/heading',
        'core/list',
        'core/list-item',
        'core/quote',
        'core/separator',
        'core/spacer',
    );

    // Check if we want to allow core blocks (can be controlled via admin setting)
    $allow_core_blocks = get_option('lindenhof_allow_core_blocks', false);

    if ($allow_core_blocks) {
        return array_merge($lindenhof_blocks, $allowed_core_blocks);
    }

    // Return only Lindenhof blocks
    return $lindenhof_blocks;
}
