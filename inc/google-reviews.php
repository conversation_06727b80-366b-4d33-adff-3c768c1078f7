<?php
/**
 * Google Reviews Integration
 * 
 * @package Lindenhof
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Get Google Reviews for a place
 * 
 * @param string $place_id Google Place ID
 * @return array|WP_Error Reviews data or error
 */
function lindenhof_get_google_reviews($place_id) {
    if (empty($place_id)) {
        return new WP_Error('no_place_id', __('Google Place ID is vereist.', 'lindenhof'));
    }
    
    // Get API key from settings
    $api_key = lindenhof_get_setting('google_api_key');
    if (empty($api_key)) {
        return new WP_Error('no_api_key', __('Google API key niet geconfigureerd.', 'lindenhof'));
    }
    
    // Check cache first
    $cache_key = 'lindenhof_google_reviews_' . md5($place_id);
    $cached_data = get_transient($cache_key);
    
    if ($cached_data !== false) {
        return $cached_data;
    }
    
    // Prepare API request
    $api_url = 'https://maps.googleapis.com/maps/api/place/details/json';
    $query_params = array(
        'place_id' => $place_id,
        'fields' => 'name,rating,user_ratings_total,reviews,url',
        'key' => $api_key,
        'language' => 'nl'
    );
    
    $response = wp_remote_get(add_query_arg($query_params, $api_url), array(
        'timeout' => 30,
        'sslverify' => true,
    ));
    
    if (is_wp_error($response)) {
        return new WP_Error('api_error', __('Fout bij communicatie met Google Places API', 'lindenhof'));
    }
    
    $response_code = wp_remote_retrieve_response_code($response);
    $response_body = wp_remote_retrieve_body($response);
    
    if ($response_code !== 200) {
        return new WP_Error('api_error', __('Onverwachte response van Google Places API', 'lindenhof'));
    }
    
    $data = json_decode($response_body, true);
    
    if (!$data || $data['status'] !== 'OK') {
        $error_message = isset($data['error_message']) ? $data['error_message'] : __('Onbekende fout bij Google Places API', 'lindenhof');
        return new WP_Error('api_error', $error_message);
    }
    
    $result = $data['result'];
    
    // Format the data
    $reviews_data = array(
        'name' => $result['name'] ?? '',
        'rating' => $result['rating'] ?? 0,
        'total_ratings' => $result['user_ratings_total'] ?? 0,
        'google_url' => $result['url'] ?? '',
        'reviews' => array()
    );
    
    // Process individual reviews
    if (isset($result['reviews']) && is_array($result['reviews'])) {
        foreach ($result['reviews'] as $review) {
            $reviews_data['reviews'][] = array(
                'author_name' => $review['author_name'] ?? '',
                'author_url' => $review['author_url'] ?? '',
                'profile_photo_url' => $review['profile_photo_url'] ?? '',
                'rating' => $review['rating'] ?? 0,
                'relative_time_description' => $review['relative_time_description'] ?? '',
                'text' => $review['text'] ?? '',
                'time' => $review['time'] ?? 0
            );
        }
    }
    
    // Cache for 1 hour
    set_transient($cache_key, $reviews_data, HOUR_IN_SECONDS);
    
    return $reviews_data;
}

/**
 * Render Google Reviews stars
 * 
 * @param float $rating Rating value (0-5)
 * @param string $css_class CSS class for the stars container
 * @return string HTML for star rating
 */
function lindenhof_render_google_stars($rating, $css_class = 'google-stars') {
    $rating = floatval($rating);
    $full_stars = floor($rating);
    $half_star = ($rating - $full_stars) >= 0.5;
    $empty_stars = 5 - $full_stars - ($half_star ? 1 : 0);
    
    $html = '<div class="' . esc_attr($css_class) . '" data-rating="' . esc_attr($rating) . '">';
    
    // Full stars
    for ($i = 0; $i < $full_stars; $i++) {
        $html .= '<span class="star star--full">★</span>';
    }
    
    // Half star
    if ($half_star) {
        $html .= '<span class="star star--half">★</span>';
    }
    
    // Empty stars
    for ($i = 0; $i < $empty_stars; $i++) {
        $html .= '<span class="star star--empty">☆</span>';
    }
    
    $html .= '</div>';
    
    return $html;
}

/**
 * Get content from other blocks for intro block
 * 
 * @param string $block_type Block type to get content from
 * @param int $post_id Post ID (optional, defaults to current post)
 * @return array|false Block content or false if not found
 */
function lindenhof_get_block_content($block_type, $post_id = null) {
    if (!$post_id) {
        $post_id = get_the_ID();
    }
    
    if (!$post_id) {
        return false;
    }
    
    $content = get_post_field('post_content', $post_id);
    $blocks = parse_blocks($content);
    
    foreach ($blocks as $block) {
        if ($block['blockName'] === 'acf/' . $block_type) {
            return $block['attrs']['data'] ?? false;
        }
    }
    
    return false;
}

/**
 * Clear Google Reviews cache
 * 
 * @param string $place_id Google Place ID (optional, clears all if not provided)
 */
function lindenhof_clear_google_reviews_cache($place_id = null) {
    if ($place_id) {
        $cache_key = 'lindenhof_google_reviews_' . md5($place_id);
        delete_transient($cache_key);
    } else {
        // Clear all Google Reviews caches
        global $wpdb;
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_lindenhof_google_reviews_%'");
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_lindenhof_google_reviews_%'");
    }
}

/**
 * Add admin action to clear Google Reviews cache
 */
add_action('wp_ajax_clear_google_reviews_cache', 'lindenhof_ajax_clear_google_reviews_cache');
function lindenhof_ajax_clear_google_reviews_cache() {
    // Check permissions
    if (!current_user_can('manage_options')) {
        wp_die(__('Onvoldoende rechten.', 'lindenhof'));
    }
    
    // Check nonce
    if (!wp_verify_nonce($_POST['nonce'], 'clear_google_reviews_cache')) {
        wp_die(__('Beveiligingscontrole gefaald.', 'lindenhof'));
    }
    
    lindenhof_clear_google_reviews_cache();
    
    wp_send_json_success(__('Google Reviews cache geleegd.', 'lindenhof'));
}

/**
 * Register REST API endpoint for Google Reviews
 */
add_action('rest_api_init', 'lindenhof_register_google_reviews_endpoint');
function lindenhof_register_google_reviews_endpoint() {
    register_rest_route('lindenhof/v1', '/google-reviews/(?P<place_id>[a-zA-Z0-9_-]+)', array(
        'methods' => 'GET',
        'callback' => 'lindenhof_rest_get_google_reviews',
        'permission_callback' => '__return_true',
        'args' => array(
            'place_id' => array(
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
            ),
        ),
    ));
}

/**
 * REST API callback for Google Reviews
 */
function lindenhof_rest_get_google_reviews($request) {
    $place_id = $request->get_param('place_id');
    
    $reviews = lindenhof_get_google_reviews($place_id);
    
    if (is_wp_error($reviews)) {
        return new WP_Error(
            $reviews->get_error_code(),
            $reviews->get_error_message(),
            array('status' => 400)
        );
    }
    
    return rest_ensure_response($reviews);
}
