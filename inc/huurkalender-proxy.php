<?php
/**
 * Lindenhof Huurkalender Proxy
 *
 * DEPRECATED: This file is no longer used since we switched to Huurkalender.nl embed widgets
 * The huurkalender block now uses direct iframe embeds instead of API calls
 *
 * @package Lindenhof
 * @version 1.0.0
 * @deprecated 1.1.0 Use Huurkalender.nl embed widgets instead
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// DEPRECATED: This code is no longer used
return;

// Register REST API endpoints
add_action('rest_api_init', 'lindenhof_register_huurkalender_endpoints');
function lindenhof_register_huurkalender_endpoints() {
    register_rest_route('lindenhof/v1', '/huurkalender/prefilter', array(
        'methods' => 'POST',
        'callback' => 'lindenhof_huurkalender_prefilter',
        'permission_callback' => 'lindenhof_verify_nonce',
        'args' => array(
            'arrival_date' => array(
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
                'validate_callback' => 'lindenhof_validate_date',
            ),
            'guests' => array(
                'required' => true,
                'type' => 'integer',
                'sanitize_callback' => 'absint',
                'validate_callback' => 'lindenhof_validate_guests',
            ),
            'nights' => array(
                'required' => false,
                'type' => 'integer',
                'default' => 1,
                'sanitize_callback' => 'absint',
            ),
        ),
    ));
    
    register_rest_route('lindenhof/v1', '/huurkalender/availability', array(
        'methods' => 'GET',
        'callback' => 'lindenhof_huurkalender_availability',
        'permission_callback' => 'lindenhof_verify_nonce',
        'args' => array(
            'month' => array(
                'required' => false,
                'type' => 'string',
                'default' => date('Y-m'),
                'sanitize_callback' => 'sanitize_text_field',
                'validate_callback' => 'lindenhof_validate_month',
            ),
        ),
    ));
}

// Verify nonce for security
function lindenhof_verify_nonce($request) {
    $nonce = $request->get_header('X-WP-Nonce');
    if (!$nonce) {
        $nonce = $request->get_param('_wpnonce');
    }
    
    return wp_verify_nonce($nonce, 'wp_rest');
}

// Validation functions
function lindenhof_validate_date($value, $request, $param) {
    $date = DateTime::createFromFormat('Y-m-d', $value);
    return $date && $date->format('Y-m-d') === $value;
}

function lindenhof_validate_guests($value, $request, $param) {
    return is_numeric($value) && $value > 0 && $value <= 50;
}

function lindenhof_validate_month($value, $request, $param) {
    return preg_match('/^\d{4}-\d{2}$/', $value);
}

// Prefilter endpoint - generates booking URL with parameters
function lindenhof_huurkalender_prefilter($request) {
    $arrival_date = $request->get_param('arrival_date');
    $guests = $request->get_param('guests');
    $nights = $request->get_param('nights') ?: 1;
    
    // Get API key from settings
    $api_key = lindenhof_get_setting('huurkalender_api_key');
    if (empty($api_key)) {
        return new WP_Error(
            'no_api_key',
            __('Huurkalender API key niet geconfigureerd.', 'lindenhof'),
            array('status' => 500)
        );
    }
    
    // Prepare API request
    $api_url = 'https://api.huurkalender.nl/v1/prefilter';
    $body = array(
        'arrival_date' => $arrival_date,
        'guests' => $guests,
        'nights' => $nights,
        'property_id' => get_option('lindenhof_property_id', ''), // Add this to admin settings if needed
    );
    
    $response = wp_remote_post($api_url, array(
        'headers' => array(
            'Authorization' => 'Bearer ' . $api_key,
            'Content-Type' => 'application/json',
        ),
        'body' => wp_json_encode($body),
        'timeout' => 30,
        'sslverify' => true,
    ));
    
    if (is_wp_error($response)) {
        return new WP_Error(
            'api_error',
            __('Fout bij communicatie met huurkalender.nl', 'lindenhof'),
            array('status' => 500)
        );
    }
    
    $response_code = wp_remote_retrieve_response_code($response);
    $response_body = wp_remote_retrieve_body($response);
    
    if ($response_code !== 200) {
        return new WP_Error(
            'api_error',
            __('Onverwachte response van huurkalender.nl', 'lindenhof'),
            array('status' => $response_code)
        );
    }
    
    $data = json_decode($response_body, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        return new WP_Error(
            'json_error',
            __('Ongeldige response van huurkalender.nl', 'lindenhof'),
            array('status' => 500)
        );
    }
    
    // Return sanitized response
    return rest_ensure_response(array(
        'success' => true,
        'booking_url' => esc_url_raw($data['booking_url'] ?? ''),
        'availability' => $data['availability'] ?? false,
        'price' => sanitize_text_field($data['price'] ?? ''),
        'message' => sanitize_text_field($data['message'] ?? ''),
    ));
}

// Availability endpoint - gets calendar availability
function lindenhof_huurkalender_availability($request) {
    $month = $request->get_param('month');
    
    // Get API key from settings
    $api_key = lindenhof_get_setting('huurkalender_api_key');
    if (empty($api_key)) {
        return new WP_Error(
            'no_api_key',
            __('Huurkalender API key niet geconfigureerd.', 'lindenhof'),
            array('status' => 500)
        );
    }
    
    // Prepare API request
    $api_url = 'https://api.huurkalender.nl/v1/availability';
    $query_params = array(
        'month' => $month,
        'property_id' => get_option('lindenhof_property_id', ''),
    );
    
    $response = wp_remote_get(add_query_arg($query_params, $api_url), array(
        'headers' => array(
            'Authorization' => 'Bearer ' . $api_key,
        ),
        'timeout' => 30,
        'sslverify' => true,
    ));
    
    if (is_wp_error($response)) {
        return new WP_Error(
            'api_error',
            __('Fout bij communicatie met huurkalender.nl', 'lindenhof'),
            array('status' => 500)
        );
    }
    
    $response_code = wp_remote_retrieve_response_code($response);
    $response_body = wp_remote_retrieve_body($response);
    
    if ($response_code !== 200) {
        return new WP_Error(
            'api_error',
            __('Onverwachte response van huurkalender.nl', 'lindenhof'),
            array('status' => $response_code)
        );
    }
    
    $data = json_decode($response_body, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        return new WP_Error(
            'json_error',
            __('Ongeldige response van huurkalender.nl', 'lindenhof'),
            array('status' => 500)
        );
    }
    
    // Return sanitized response
    $availability = array();
    if (isset($data['availability']) && is_array($data['availability'])) {
        foreach ($data['availability'] as $date => $status) {
            $availability[sanitize_text_field($date)] = sanitize_text_field($status);
        }
    }
    
    return rest_ensure_response(array(
        'success' => true,
        'availability' => $availability,
        'month' => sanitize_text_field($month),
    ));
}

// Legacy AJAX endpoints for backward compatibility
add_action('wp_ajax_lindenhof_huurkalender_prefilter', 'lindenhof_ajax_huurkalender_prefilter');
add_action('wp_ajax_nopriv_lindenhof_huurkalender_prefilter', 'lindenhof_ajax_huurkalender_prefilter');

function lindenhof_ajax_huurkalender_prefilter() {
    // Verify nonce
    if (!check_ajax_referer('lindenhof_nonce', 'nonce', false)) {
        wp_die(__('Beveiligingscontrole gefaald.', 'lindenhof'), 403);
    }
    
    // Create a mock request object for the REST endpoint
    $request = new WP_REST_Request('POST', '/lindenhof/v1/huurkalender/prefilter');
    $request->set_param('arrival_date', sanitize_text_field($_POST['arrival_date'] ?? ''));
    $request->set_param('guests', absint($_POST['guests'] ?? 0));
    $request->set_param('nights', absint($_POST['nights'] ?? 1));
    
    $response = lindenhof_huurkalender_prefilter($request);
    
    if (is_wp_error($response)) {
        wp_send_json_error($response->get_error_message());
    } else {
        wp_send_json_success($response->get_data());
    }
}
