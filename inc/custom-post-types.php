<?php
/**
 * Lindenhof Custom Post Types
 * 
 * @package Lindenhof
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Register custom post types
add_action('init', 'lindenhof_register_post_types');
function lindenhof_register_post_types() {
    
    // Faciliteiten
    register_post_type('faciliteiten', array(
        'labels' => array(
            'name' => __('Faciliteiten', 'lindenhof'),
            'singular_name' => __('Faciliteit', 'lindenhof'),
            'add_new' => __('Nieuwe faciliteit', 'lindenhof'),
            'add_new_item' => __('Nieuwe faciliteit toevoegen', 'lindenhof'),
            'edit_item' => __('Faciliteit bewerken', 'lindenhof'),
            'new_item' => __('Nieuwe faciliteit', 'lindenhof'),
            'view_item' => __('Faciliteit bekijken', 'lindenhof'),
            'search_items' => __('Faciliteiten zoeken', 'lindenhof'),
            'not_found' => __('Geen faciliteiten gevonden', 'lindenhof'),
            'not_found_in_trash' => __('Geen faciliteiten in prullenbak', 'lindenhof'),
            'menu_name' => __('Faciliteiten', 'lindenhof'),
        ),
        'public' => false,
        'show_ui' => true,
        'show_in_menu' => true,
        'menu_position' => 25,
        'menu_icon' => 'dashicons-admin-home',
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt'),
        'has_archive' => false,
        'rewrite' => false,
        'publicly_queryable' => false,
        'exclude_from_search' => true,
    ));
    
    // Eten & Drinken
    register_post_type('eten_drinken', array(
        'labels' => array(
            'name' => __('Eten & Drinken', 'lindenhof'),
            'singular_name' => __('Restaurant', 'lindenhof'),
            'add_new' => __('Nieuw restaurant', 'lindenhof'),
            'add_new_item' => __('Nieuw restaurant toevoegen', 'lindenhof'),
            'edit_item' => __('Restaurant bewerken', 'lindenhof'),
            'new_item' => __('Nieuw restaurant', 'lindenhof'),
            'view_item' => __('Restaurant bekijken', 'lindenhof'),
            'search_items' => __('Restaurants zoeken', 'lindenhof'),
            'not_found' => __('Geen restaurants gevonden', 'lindenhof'),
            'not_found_in_trash' => __('Geen restaurants in prullenbak', 'lindenhof'),
            'menu_name' => __('Eten & Drinken', 'lindenhof'),
        ),
        'public' => false,
        'show_ui' => true,
        'show_in_menu' => true,
        'menu_position' => 26,
        'menu_icon' => 'dashicons-food',
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt'),
        'has_archive' => false,
        'rewrite' => false,
    ));
    
    // Omgeving & Activiteiten
    register_post_type('activiteiten', array(
        'labels' => array(
            'name' => __('Activiteiten', 'lindenhof'),
            'singular_name' => __('Activiteit', 'lindenhof'),
            'add_new' => __('Nieuwe activiteit', 'lindenhof'),
            'add_new_item' => __('Nieuwe activiteit toevoegen', 'lindenhof'),
            'edit_item' => __('Activiteit bewerken', 'lindenhof'),
            'new_item' => __('Nieuwe activiteit', 'lindenhof'),
            'view_item' => __('Activiteit bekijken', 'lindenhof'),
            'search_items' => __('Activiteiten zoeken', 'lindenhof'),
            'not_found' => __('Geen activiteiten gevonden', 'lindenhof'),
            'not_found_in_trash' => __('Geen activiteiten in prullenbak', 'lindenhof'),
            'menu_name' => __('Activiteiten', 'lindenhof'),
        ),
        'public' => false,
        'show_ui' => true,
        'show_in_menu' => true,
        'menu_position' => 27,
        'menu_icon' => 'dashicons-location-alt',
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt'),
        'has_archive' => false,
        'rewrite' => false,
    ));
    
    // Galerij
    register_post_type('galerij', array(
        'labels' => array(
            'name' => __('Galerij', 'lindenhof'),
            'singular_name' => __('Foto', 'lindenhof'),
            'add_new' => __('Nieuwe foto', 'lindenhof'),
            'add_new_item' => __('Nieuwe foto toevoegen', 'lindenhof'),
            'edit_item' => __('Foto bewerken', 'lindenhof'),
            'new_item' => __('Nieuwe foto', 'lindenhof'),
            'view_item' => __('Foto bekijken', 'lindenhof'),
            'search_items' => __('Foto\'s zoeken', 'lindenhof'),
            'not_found' => __('Geen foto\'s gevonden', 'lindenhof'),
            'not_found_in_trash' => __('Geen foto\'s in prullenbak', 'lindenhof'),
            'menu_name' => __('Galerij', 'lindenhof'),
        ),
        'public' => false,
        'show_ui' => true,
        'show_in_menu' => true,
        'menu_position' => 28,
        'menu_icon' => 'dashicons-images-alt2',
        'supports' => array('title', 'thumbnail'),
        'has_archive' => false,
        'rewrite' => false,
    ));
}

// Register ACF field groups for custom post types
add_action('acf/init', 'lindenhof_register_cpt_field_groups');
function lindenhof_register_cpt_field_groups() {
    if (!function_exists('acf_add_local_field_group')) {
        return;
    }
    
    // Faciliteiten Fields - Removed old fields, now using block-based system
    
    // Eten & Drinken Fields
    acf_add_local_field_group(array(
        'key' => 'group_eten_drinken',
        'title' => 'Restaurant Details',
        'fields' => array(
            array(
                'key' => 'field_restaurant_afstand',
                'label' => 'Afstand',
                'name' => 'afstand',
                'type' => 'text',
                'placeholder' => 'bijv. 5 minuten lopen',
            ),
            array(
                'key' => 'field_restaurant_website',
                'label' => 'Website',
                'name' => 'website',
                'type' => 'url',
            ),
            array(
                'key' => 'field_restaurant_telefoon',
                'label' => 'Telefoonnummer',
                'name' => 'telefoon',
                'type' => 'text',
            ),
            array(
                'key' => 'field_restaurant_adres',
                'label' => 'Adres',
                'name' => 'adres',
                'type' => 'textarea',
                'rows' => 2,
            ),
            array(
                'key' => 'field_restaurant_type',
                'label' => 'Type keuken',
                'name' => 'type_keuken',
                'type' => 'select',
                'choices' => array(
                    'nederlands' => 'Nederlands',
                    'italiaans' => 'Italiaans',
                    'frans' => 'Frans',
                    'aziatisch' => 'Aziatisch',
                    'internationaal' => 'Internationaal',
                    'cafe' => 'Café/Bar',
                    'fastfood' => 'Fastfood',
                ),
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'eten_drinken',
                ),
            ),
        ),
    ));
    
    // Activiteiten Fields
    acf_add_local_field_group(array(
        'key' => 'group_activiteiten',
        'title' => 'Activiteit Details',
        'fields' => array(
            array(
                'key' => 'field_activiteit_afstand',
                'label' => 'Afstand',
                'name' => 'afstand',
                'type' => 'text',
                'placeholder' => 'bijv. 10 minuten rijden',
            ),
            array(
                'key' => 'field_activiteit_website',
                'label' => 'Website',
                'name' => 'website',
                'type' => 'url',
            ),
            array(
                'key' => 'field_activiteit_kosten',
                'label' => 'Kosten',
                'name' => 'kosten',
                'type' => 'text',
                'placeholder' => 'bijv. €15 per persoon',
            ),
            array(
                'key' => 'field_activiteit_seizoen',
                'label' => 'Seizoen',
                'name' => 'seizoen',
                'type' => 'select',
                'choices' => array(
                    'heel_jaar' => 'Heel jaar',
                    'lente' => 'Lente',
                    'zomer' => 'Zomer',
                    'herfst' => 'Herfst',
                    'winter' => 'Winter',
                ),
                'default_value' => 'heel_jaar',
            ),
            array(
                'key' => 'field_activiteit_categorie',
                'label' => 'Categorie',
                'name' => 'categorie',
                'type' => 'select',
                'choices' => array(
                    'natuur' => 'Natuur',
                    'cultuur' => 'Cultuur',
                    'sport' => 'Sport',
                    'familie' => 'Familie',
                    'avontuur' => 'Avontuur',
                    'ontspanning' => 'Ontspanning',
                ),
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'activiteiten',
                ),
            ),
        ),
    ));
    
    // Galerij Fields
    acf_add_local_field_group(array(
        'key' => 'group_galerij',
        'title' => 'Foto Details',
        'fields' => array(
            array(
                'key' => 'field_foto_categorie',
                'label' => 'Categorie',
                'name' => 'categorie',
                'type' => 'select',
                'choices' => array(
                    'accommodatie' => 'Accommodatie',
                    'omgeving' => 'Omgeving',
                    'faciliteiten' => 'Faciliteiten',
                    'activiteiten' => 'Activiteiten',
                    'eten_drinken' => 'Eten & Drinken',
                ),
            ),
            array(
                'key' => 'field_foto_volgorde',
                'label' => 'Volgorde',
                'name' => 'volgorde',
                'type' => 'number',
                'default_value' => 0,
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'galerij',
                ),
            ),
        ),
    ));
}

// Exclude custom post types from Yoast SEO sitemap
add_filter('wpseo_sitemap_exclude_post_type', 'lindenhof_exclude_post_types_from_sitemap', 10, 2);
function lindenhof_exclude_post_types_from_sitemap($excluded, $post_type) {
    $excluded_post_types = array('faciliteiten', 'eten_drinken', 'activiteiten', 'galerij');

    if (in_array($post_type, $excluded_post_types)) {
        return true;
    }

    return $excluded;
}

// Prevent direct access to custom post type URLs
add_action('template_redirect', 'lindenhof_redirect_custom_post_types');
function lindenhof_redirect_custom_post_types() {
    if (is_singular(array('faciliteiten', 'eten_drinken', 'activiteiten', 'galerij'))) {
        wp_redirect(home_url(), 301);
        exit;
    }
}
