<?php
// Get ACF fields
$title = get_field('title');

// Faciliteiten data - 3 categories like in screenshot
$doelgroepen = array(
    'Familie' => get_field('doelgroep_familie'),
    'Vriendengroep boven de 30 jaar' => get_field('doelgroep_vrienden_boven_30'),
    'Studenten' => get_field('doelgroep_studenten'),
    'Feestgroep' => get_field('doelgroep_feestgroep'),
    'Jongeren onder de 25 jaar' => get_field('doelgroep_jongeren_onder_25'),
    'School / jeugdkamp' => get_field('doelgroep_school_jeugdkamp'),
    'Sportvereniging' => get_field('doelgroep_sportvereniging'),
    'Voetbalteam' => get_field('doelgroep_voetbalteam'),
    'Vriendengroep onder de 30 jaar' => get_field('doelgroep_vrienden_onder_30'),
);

$algemeen = array(
    'WiFi' => get_field('algemeen_wifi'),
    '2 Douches' => get_field('algemeen_douches'),
    '2 Toiletten' => get_field('algemeen_toiletten'),
    '2 Wastafels' => get_field('algemeen_wastafels'),
    '1 Kinderbedjes' => get_field('algemeen_kinderbedjes'),
    '1 Kinderstoelen' => get_field('algemeen_kinderstoelen'),
    'Traphekjes' => get_field('algemeen_traphekjes'),
);

$faciliteiten = array(
    'Woonkamer' => get_field('faciliteiten_woonkamer'),
    'Televisie' => get_field('faciliteiten_televisie'),
    'Keuken' => get_field('faciliteiten_keuken'),
    '5 Aantal gaspitten / kookplaten' => get_field('faciliteiten_gaspitten'),
    'Koelkast' => get_field('faciliteiten_koelkast'),
    'Diepvries' => get_field('faciliteiten_diepvries'),
    'Magnetron' => get_field('faciliteiten_magnetron'),
    'Oven' => get_field('faciliteiten_oven'),
    'Vaatwasmaschine' => get_field('faciliteiten_vaatwasser'),
);
?>

<section class="faciliteiten-block">
    <div class="container">
        
        <?php if ($title): ?>
            <h2><?php echo esc_html($title); ?></h2>
        <?php endif; ?>
        
        <div class="faciliteiten-grid">
            
            <div class="faciliteiten-column">
                <h3>Doelgroepen</h3>
                <ul>
                    <?php foreach ($doelgroepen as $label => $value): ?>
                        <li class="<?php echo $value ? 'yes' : 'no'; ?>">
                            <span class="icon"><?php echo $value ? '✓' : '✗'; ?></span>
                            <?php echo esc_html($label); ?>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>
            
            <div class="faciliteiten-column">
                <h3>Algemeen</h3>
                <ul>
                    <?php foreach ($algemeen as $label => $value): ?>
                        <li class="<?php echo $value ? 'yes' : 'no'; ?>">
                            <span class="icon"><?php echo $value ? '✓' : '✗'; ?></span>
                            <?php echo esc_html($label); ?>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>
            
            <div class="faciliteiten-column">
                <h3>Faciliteiten</h3>
                <ul>
                    <?php foreach ($faciliteiten as $label => $value): ?>
                        <li class="<?php echo $value ? 'yes' : 'no'; ?>">
                            <span class="icon"><?php echo $value ? '✓' : '✗'; ?></span>
                            <?php echo esc_html($label); ?>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>
            
        </div>
        
    </div>
</section>
