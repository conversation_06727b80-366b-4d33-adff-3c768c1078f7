<?php
/**
 * Faciliteiten Block Template
 * 
 * @package Lindenhof
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get ACF fields
$title = get_field('title');
$facilities = get_field('facilities');

// Generate unique ID for this block instance
$block_id = $block_id ?? lindenhof_unique_id('faciliteiten-block');

// Build CSS classes
$css_classes = lindenhof_bem_classes('faciliteiten-block', '', '', array('lindenhof-block'));
?>

<section 
    id="<?php echo esc_attr($block_id); ?>" 
    class="<?php echo esc_attr($css_classes); ?>"
    <?php echo lindenhof_swup_attributes(); ?>
>
    <div class="<?php echo lindenhof_bem_classes('faciliteiten-block', 'container'); ?>">
        
        <?php if ($title): ?>
            <header class="<?php echo lindenhof_bem_classes('faciliteiten-block', 'header'); ?>">
                <h2 class="<?php echo lindenhof_bem_classes('faciliteiten-block', 'title'); ?>">
                    <?php echo lindenhof_safe_text($title); ?>
                </h2>
            </header>
        <?php endif; ?>
        
        <?php if ($facilities && is_array($facilities)): ?>
            <div class="<?php echo lindenhof_bem_classes('faciliteiten-block', 'grid'); ?>">
                <?php foreach ($facilities as $index => $facility): ?>
                    <?php
                    $facility_icon = $facility['icon'] ?? null;
                    $facility_title = $facility['title'] ?? '';
                    $facility_description = $facility['description'] ?? '';
                    $facility_id = lindenhof_unique_id('facility');
                    ?>
                    
                    <article 
                        id="<?php echo esc_attr($facility_id); ?>"
                        class="<?php echo lindenhof_bem_classes('faciliteiten-block', 'item'); ?>"
                    >
                        
                        <?php if ($facility_icon): ?>
                            <div class="<?php echo lindenhof_bem_classes('faciliteiten-block', 'item-icon'); ?>">
                                <?php echo lindenhof_safe_image(
                                    $facility_icon, 
                                    'thumbnail', 
                                    lindenhof_bem_classes('faciliteiten-block', 'icon-image'),
                                    $facility_title
                                ); ?>
                            </div>
                        <?php endif; ?>
                        
                        <div class="<?php echo lindenhof_bem_classes('faciliteiten-block', 'item-content'); ?>">
                            
                            <?php if ($facility_title): ?>
                                <h3 class="<?php echo lindenhof_bem_classes('faciliteiten-block', 'item-title'); ?>">
                                    <?php echo lindenhof_safe_text($facility_title); ?>
                                </h3>
                            <?php endif; ?>
                            
                            <?php if ($facility_description): ?>
                                <p class="<?php echo lindenhof_bem_classes('faciliteiten-block', 'item-description'); ?>">
                                    <?php echo lindenhof_safe_text($facility_description); ?>
                                </p>
                            <?php endif; ?>
                            
                        </div>
                        
                    </article>
                    
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
        
    </div>
</section>
