<?php
/**
 * Faciliteiten Block Template
 * 
 * @package Lindenhof
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get ACF fields
$title = get_field('title');

// Get all facility fields in one list
$alle_faciliteiten = array(
    'Familie' => get_field('faciliteit_familie') ?: get_field('doelgroep_familie'),
    'Vriendengroep boven de 30 jaar' => get_field('faciliteit_vrienden_boven_30') ?: get_field('doelgroep_vrienden_boven_30'),
    'Studenten' => get_field('faciliteit_studenten') ?: get_field('doelgroep_studenten'),
    'Feestgroep' => get_field('faciliteit_feestgroep') ?: get_field('doelgroep_feestgroep'),
    'Jongeren onder de 25 jaar' => get_field('faciliteit_jongeren_onder_25') ?: get_field('doelgroep_jongeren_onder_25'),
    'School / jeugdkamp' => get_field('faciliteit_school_jeugdkamp') ?: get_field('doelgroep_school_jeugdkamp'),
    'Sportvereniging' => get_field('faciliteit_sportvereniging') ?: get_field('doelgroep_sportvereniging'),
    'Voetbalteam' => get_field('faciliteit_voetbalteam') ?: get_field('doelgroep_voetbalteam'),
    'Vriendengroep onder de 30 jaar' => get_field('faciliteit_vrienden_onder_30') ?: get_field('doelgroep_vrienden_onder_30'),
    'WiFi' => get_field('faciliteit_wifi') ?: get_field('algemeen_wifi'),
    '2 Douches' => get_field('faciliteit_douches') ?: get_field('algemeen_douches'),
    '2 Toiletten' => get_field('faciliteit_toiletten') ?: get_field('algemeen_toiletten'),
    '2 Wastafels' => get_field('faciliteit_wastafels') ?: get_field('algemeen_wastafels'),
    '1 Kinderbedjes' => get_field('faciliteit_kinderbedjes') ?: get_field('algemeen_kinderbedjes'),
    '1 Kinderstoelen' => get_field('faciliteit_kinderstoelen') ?: get_field('algemeen_kinderstoelen'),
    'Traphekjes' => get_field('faciliteit_traphekjes') ?: get_field('algemeen_traphekjes'),
    'Woonkamer' => get_field('faciliteit_woonkamer') ?: get_field('faciliteiten_woonkamer'),
    'Televisie' => get_field('faciliteit_televisie') ?: get_field('faciliteiten_televisie'),
    'Keuken' => get_field('faciliteit_keuken') ?: get_field('faciliteiten_keuken'),
    '5 Aantal gaspitten / kookplaten' => get_field('faciliteit_gaspitten') ?: get_field('faciliteiten_gaspitten'),
    'Koelkast' => get_field('faciliteit_koelkast') ?: get_field('faciliteiten_koelkast'),
    'Diepvries' => get_field('faciliteit_diepvries') ?: get_field('faciliteiten_diepvries'),
    'Magnetron' => get_field('faciliteit_magnetron') ?: get_field('faciliteiten_magnetron'),
    'Oven' => get_field('faciliteit_oven') ?: get_field('faciliteiten_oven'),
    'Vaatwasser' => get_field('faciliteit_vaatwasser') ?: get_field('faciliteiten_vaatwasser'),
);

// Generate unique ID for this block instance
$block_id = $block_id ?? lindenhof_unique_id('faciliteiten-block');

// Build CSS classes
$css_classes = lindenhof_bem_classes('faciliteiten-block', '', '', array('lindenhof-block'));
?>

<section 
    id="<?php echo esc_attr($block_id); ?>" 
    class="<?php echo esc_attr($css_classes); ?>"
    <?php echo lindenhof_swup_attributes(); ?>
>
    <div class="<?php echo lindenhof_bem_classes('faciliteiten-block', 'container'); ?>">
        
        <?php if ($title): ?>
            <h2 class="<?php echo lindenhof_bem_classes('faciliteiten-block', 'title'); ?>">
                <?php echo lindenhof_safe_text($title); ?>
            </h2>
        <?php endif; ?>
        
        <div class="<?php echo lindenhof_bem_classes('faciliteiten-block', 'items'); ?>">
            <?php foreach ($alle_faciliteiten as $label => $value): ?>
                <div class="<?php echo lindenhof_bem_classes('faciliteiten-block', 'item'); ?> <?php echo $value ? lindenhof_bem_classes('faciliteiten-block', 'item', 'yes') : lindenhof_bem_classes('faciliteiten-block', 'item', 'no'); ?>">
                    <span class="<?php echo lindenhof_bem_classes('faciliteiten-block', 'item-icon'); ?>">
                        <?php echo $value ? '✓' : '✗'; ?>
                    </span>
                    <span class="<?php echo lindenhof_bem_classes('faciliteiten-block', 'item-label'); ?>">
                        <?php echo esc_html($label); ?>
                    </span>
                </div>
            <?php endforeach; ?>
        </div>
        
    </div>
</section>
