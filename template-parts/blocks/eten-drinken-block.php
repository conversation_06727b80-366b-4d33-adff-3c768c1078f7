<?php
/**
 * <PERSON>ten & Drinken Block Template
 * 
 * @package Lindenhof
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get ACF fields
$title = get_field('title');
$content = get_field('content');
$restaurants = get_field('restaurants');

// Generate unique ID for this block instance
$block_id = $block_id ?? lindenhof_unique_id('eten-drinken-block');

// Build CSS classes
$css_classes = lindenhof_bem_classes('eten-drinken-block', '', '', array('lindenhof-block'));
?>

<section 
    id="<?php echo esc_attr($block_id); ?>" 
    class="<?php echo esc_attr($css_classes); ?>"
    <?php echo lindenhof_swup_attributes(); ?>
>
    <div class="<?php echo lindenhof_bem_classes('eten-drinken-block', 'container'); ?>">
        
        <?php if ($title): ?>
            <header class="<?php echo lindenhof_bem_classes('eten-drinken-block', 'header'); ?>">
                <h2 class="<?php echo lindenhof_bem_classes('eten-drinken-block', 'title'); ?>">
                    <?php echo lindenhof_safe_text($title); ?>
                </h2>
            </header>
        <?php endif; ?>
        
        <?php if ($content): ?>
            <div class="<?php echo lindenhof_bem_classes('eten-drinken-block', 'content'); ?>">
                <?php echo lindenhof_safe_text($content, true); ?>
            </div>
        <?php endif; ?>
        
        <?php if ($restaurants && is_array($restaurants)): ?>
            <div class="<?php echo lindenhof_bem_classes('eten-drinken-block', 'restaurants'); ?>">
                
                <h3 class="<?php echo lindenhof_bem_classes('eten-drinken-block', 'restaurants-title'); ?>">
                    <?php echo esc_html__('Restaurants in de buurt', 'lindenhof'); ?>
                </h3>
                
                <div class="<?php echo lindenhof_bem_classes('eten-drinken-block', 'restaurants-grid'); ?>">
                    <?php foreach ($restaurants as $index => $restaurant): ?>
                        <?php
                        $restaurant_name = $restaurant['name'] ?? '';
                        $restaurant_description = $restaurant['description'] ?? '';
                        $restaurant_distance = $restaurant['distance'] ?? '';
                        $restaurant_link = $restaurant['link'] ?? '';
                        $restaurant_id = lindenhof_unique_id('restaurant');
                        ?>
                        
                        <article 
                            id="<?php echo esc_attr($restaurant_id); ?>"
                            class="<?php echo lindenhof_bem_classes('eten-drinken-block', 'restaurant'); ?>"
                        >
                            
                            <div class="<?php echo lindenhof_bem_classes('eten-drinken-block', 'restaurant-content'); ?>">
                                
                                <?php if ($restaurant_name): ?>
                                    <h4 class="<?php echo lindenhof_bem_classes('eten-drinken-block', 'restaurant-name'); ?>">
                                        <?php if ($restaurant_link): ?>
                                            <a 
                                                href="<?php echo lindenhof_safe_url($restaurant_link); ?>" 
                                                target="_blank" 
                                                rel="noopener noreferrer"
                                                class="<?php echo lindenhof_bem_classes('eten-drinken-block', 'restaurant-link'); ?>"
                                            >
                                                <?php echo lindenhof_safe_text($restaurant_name); ?>
                                            </a>
                                        <?php else: ?>
                                            <?php echo lindenhof_safe_text($restaurant_name); ?>
                                        <?php endif; ?>
                                    </h4>
                                <?php endif; ?>
                                
                                <?php if ($restaurant_description): ?>
                                    <p class="<?php echo lindenhof_bem_classes('eten-drinken-block', 'restaurant-description'); ?>">
                                        <?php echo lindenhof_safe_text($restaurant_description); ?>
                                    </p>
                                <?php endif; ?>
                                
                                <?php if ($restaurant_distance): ?>
                                    <p class="<?php echo lindenhof_bem_classes('eten-drinken-block', 'restaurant-distance'); ?>">
                                        <span class="<?php echo lindenhof_bem_classes('eten-drinken-block', 'distance-label'); ?>">
                                            <?php echo esc_html__('Afstand:', 'lindenhof'); ?>
                                        </span>
                                        <span class="<?php echo lindenhof_bem_classes('eten-drinken-block', 'distance-value'); ?>">
                                            <?php echo lindenhof_safe_text($restaurant_distance); ?>
                                        </span>
                                    </p>
                                <?php endif; ?>
                                
                            </div>
                            
                        </article>
                        
                    <?php endforeach; ?>
                </div>
                
            </div>
        <?php endif; ?>
        
    </div>
</section>
