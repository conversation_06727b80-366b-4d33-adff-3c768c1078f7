// Huurkalender Block Styling
// Responsive styling for Huurkalender.nl widgets

@import '../../../assets/less/vw_values.less';
@import '../../../assets/less/constants.less';

.huurkalender-block {
    padding: @vw50 0;

    &__container {
        max-width: @vw1200;
        margin: 0 auto;
        padding: 0 @vw20;

        @media (max-width: 580px) {
            padding: 0 @vw15-580;
        }
    }

    &__header {
        text-align: center;
        margin-bottom: @vw40;
    }

    &__title {
        font-size: @vw32;
        font-weight: 600;
        color: @primaryColor;
        margin-bottom: @vw15;

        @media (max-width: 1160px) {
            font-size: @vw32-1160;
        }

        @media (max-width: 580px) {
            font-size: @vw28-580;
        }
    }

    &__subtitle {
        font-size: @vw18;
        color: @grey;
        margin: 0;

        @media (max-width: 1160px) {
            font-size: @vw18-1160;
        }

        @media (max-width: 580px) {
            font-size: @vw16-580;
        }
    }

    &__content {
        display: grid;
        gap: @vw40;

        @media (min-width: 1161px) {
            grid-template-columns: 1fr 1fr;
            gap: @vw60;
        }

        @media (max-width: 1160px) {
            gap: @vw40-1160;
        }

        @media (max-width: 580px) {
            gap: @vw30-580;
        }
    }

    &__booking-form,
    &__calendar {
        background: @hardWhite;
        border-radius: @vw12;
        padding: @vw30;
        box-shadow: 0 @vw4 @vw20 rgba(0, 0, 0, 0.1);

        @media (max-width: 1160px) {
            padding: @vw30-1160;
            border-radius: @vw12-1160;
        }

        @media (max-width: 580px) {
            padding: @vw20-580;
            border-radius: @vw12-580;
        }
    }

    &__form-title,
    &__calendar-title {
        font-size: @vw24;
        font-weight: 600;
        color: @primaryColor;
        margin-bottom: @vw20;
        text-align: center;

        @media (max-width: 1160px) {
            font-size: @vw24-1160;
            margin-bottom: @vw20-1160;
        }

        @media (max-width: 580px) {
            font-size: @vw20-580;
            margin-bottom: @vw15-580;
        }
    }
    
    &__booking-widget,
    &__calendar-widget {
        position: relative;

        // Huurkalender.nl embed styling
        .huurkalender-embed {
            border-radius: @vw8;
            overflow: hidden;

            iframe {
                border: none;
                border-radius: @vw8;
                width: 100%;
                min-height: 400px;

                @media (max-width: 1160px) {
                    border-radius: @vw8-1160;
                }

                @media (max-width: 580px) {
                    min-height: 350px;
                    border-radius: @vw8-580;
                }
            }

            // Calendar specific styling
            &.huurkalender-embed-container_calendar iframe {
                min-height: 600px;

                @media (max-width: 580px) {
                    min-height: 500px;
                }
            }

            // Booking widget specific styling
            &.huurkalender-embed-container_booking iframe {
                min-height: 400px;

                @media (max-width: 580px) {
                    min-height: 350px;
                }
            }
        }

        // Loading state
        &:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease;
        }

        &.loading:before {
            opacity: 1;
            pointer-events: all;
        }
    }

    // Single column layout when only one widget is shown
    &__content:has(.huurkalender-block__booking-form:only-child),
    &__content:has(.huurkalender-block__calendar:only-child) {
        @media (min-width: 1161px) {
            grid-template-columns: 1fr;
            max-width: 800px;
            margin: 0 auto;
        }
    }

    // Error state styling
    .huurkalender-error {
        text-align: center;
        padding: @vw30;
        color: @grey;
        font-style: italic;

        @media (max-width: 1160px) {
            padding: @vw30-1160;
        }

        @media (max-width: 580px) {
            padding: @vw20-580;
        }
    }
}

// Override Huurkalender.nl default styles to match theme
.huurkalender-embed {
    // Ensure proper font inheritance
    font-family: inherit !important;

    // Remove any default margins/padding
    margin: 0 !important;
    padding: 0 !important;

    // Ensure responsive behavior
    max-width: 100% !important;

    iframe {
        // Smooth loading transition
        opacity: 0;
        transition: opacity 0.3s ease;

        &.loaded {
            opacity: 1;
        }
    }
}
