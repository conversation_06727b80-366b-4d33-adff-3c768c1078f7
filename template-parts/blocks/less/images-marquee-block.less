// out: false
.imagesMarqueeBlock {
    .marquee {
        white-space: nowrap;
        .itemsContainer {
            display: inline-block;
            .item {
                display: inline-block;
                height: auto;
                position: relative;
                overflow: hidden;
                .rounded(@vw20);
                width: 50vw;
                margin: 0 @vw8;
                .innerImage {
                    .paddingRatio(883, 563);
                    height: 0;
                    width: 100%;
                }
                img {
                    position: absolute;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    pointer-events: none;
                    object-fit: cover;
                }
            }
        }
    }
}

@media (max-width: 1160px) {
    .imagesMarqueeBlock {
      .marquee {
        .itemsContainer {
          .item {
            height: @vw100-1160 * 3.5; // Kleinere hoogte voor betere schaalbaarheid
            width: (@vw112-1160 * 1.8) + (@vw16-1160 * 2);
            margin: 0 @vw6-1160;
  
            &:nth-child(even) {
              width: (@vw112-1160 * 2.5) + (@vw16-1160 * 3);
            }
          }
        }
      }
    }
  }
  
  @media (max-width: 580px) {
    .imagesMarqueeBlock {
      .marquee {
        .itemsContainer {
          .item {
            height: @vw100-580 * 3.5; // Kleinere hoogte voor betere schaalbaarheid
            width: (@vw112-580 * 1.8) + (@vw16-580 * 2);
            margin: 0 @vw6-580;
  
            &:nth-child(even) {
              width: (@vw112-580 * 2.5) + (@vw16-580 * 3);
            }
          }
        }
      }
    }
  }
  