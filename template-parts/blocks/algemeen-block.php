<?php
/**
 * Algemeen Block Template
 * 
 * @package Lindenhof
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get ACF fields
$title = get_field('title');
$content = get_field('content');
$image = get_field('image');
$image_position = get_field('image_position') ?: 'right';

// Generate unique ID for this block instance
$block_id = $block_id ?? lindenhof_unique_id('algemeen-block');

// Build CSS classes
$css_classes = lindenhof_bem_classes('algemeen-block', '', $image_position, array('lindenhof-block'));
?>

<section 
    id="<?php echo esc_attr($block_id); ?>" 
    class="<?php echo esc_attr($css_classes); ?>"
    <?php echo lindenhof_swup_attributes(); ?>
>
    <div class="<?php echo lindenhof_bem_classes('algemeen-block', 'container'); ?>">
        
        <div class="<?php echo lindenhof_bem_classes('algemeen-block', 'content-wrapper'); ?>">
            
            <div class="<?php echo lindenhof_bem_classes('algemeen-block', 'content'); ?>">
                
                <?php if ($title): ?>
                    <header class="<?php echo lindenhof_bem_classes('algemeen-block', 'header'); ?>">
                        <h2 class="<?php echo lindenhof_bem_classes('algemeen-block', 'title'); ?>">
                            <?php echo lindenhof_safe_text($title); ?>
                        </h2>
                    </header>
                <?php endif; ?>
                
                <?php if ($content): ?>
                    <div class="<?php echo lindenhof_bem_classes('algemeen-block', 'text'); ?>">
                        <?php echo lindenhof_safe_text($content, true); ?>
                    </div>
                <?php endif; ?>
                
            </div>
            
            <?php if ($image): ?>
                <div class="<?php echo lindenhof_bem_classes('algemeen-block', 'image'); ?>">
                    <figure class="<?php echo lindenhof_bem_classes('algemeen-block', 'figure'); ?>">
                        <?php echo lindenhof_safe_image(
                            $image, 
                            'large', 
                            lindenhof_bem_classes('algemeen-block', 'image-element'),
                            $image['alt'] ?? $title
                        ); ?>
                        
                        <?php if (!empty($image['caption'])): ?>
                            <figcaption class="<?php echo lindenhof_bem_classes('algemeen-block', 'caption'); ?>">
                                <?php echo lindenhof_safe_text($image['caption']); ?>
                            </figcaption>
                        <?php endif; ?>
                    </figure>
                </div>
            <?php endif; ?>
            
        </div>
        
    </div>
</section>
