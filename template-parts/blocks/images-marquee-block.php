<?php
    $size = 'medium_large'; 
    $images = get_field('images');
?>
<section class="imagesMarqueeBlock <?php the_field("background") ?>" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
    <div class="marqueeWrapper" data-init>
        <div class="marquee" data-marquee data-marquee-direction="right" data-marquee-speed="25" data-marquee-scroll-speed="5" data-marquee-swipe="true">
            <?php if( $images ): ?>
                <?php if( count($images) > 1 ): ?>
                    <div class="marqueeScroll">
                        <div class="itemsContainer">
                            <?php foreach( $images as $image ): ?>
                                <div class="item">
                                    <div class="innerImage">
                                        <img class="lazy" data-src="<?php echo esc_url($image['sizes'][$size]); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <div class="itemsContainer">
                            <?php foreach( $images as $image ): ?>
                                <div class="item">
                                    <div class="innerImage">
                                        <img class="lazy" data-src="<?php echo esc_url($image['sizes'][$size]); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <div class="itemsContainer">
                            <?php foreach( $images as $image ): ?>
                                <div class="item">
                                    <div class="innerImage">
                                        <img class="lazy" data-src="<?php echo esc_url($image['sizes'][$size]); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <div class="itemsContainer">
                            <?php foreach( $images as $image ): ?>
                                <div class="item">
                                    <div class="innerImage">
                                        <img class="lazy" data-src="<?php echo esc_url($image['sizes'][$size]); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</section>
