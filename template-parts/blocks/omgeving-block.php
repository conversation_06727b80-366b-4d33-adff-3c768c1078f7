<?php
/**
 * Omgeving Block Template
 * 
 * @package Lindenhof
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get ACF fields
$title = get_field('title');
$content = get_field('content');
$activities = get_field('activities');

// Generate unique ID for this block instance
$block_id = $block_id ?? lindenhof_unique_id('omgeving-block');

// Build CSS classes
$css_classes = lindenhof_bem_classes('omgeving-block', '', '', array('lindenhof-block'));
?>

<section 
    id="<?php echo esc_attr($block_id); ?>" 
    class="<?php echo esc_attr($css_classes); ?>"
    <?php echo lindenhof_swup_attributes(); ?>
>
    <div class="<?php echo lindenhof_bem_classes('omgeving-block', 'container'); ?>">
        
        <?php if ($title): ?>
            <header class="<?php echo lindenhof_bem_classes('omgeving-block', 'header'); ?>">
                <h2 class="<?php echo lindenhof_bem_classes('omgeving-block', 'title'); ?>">
                    <?php echo lindenhof_safe_text($title); ?>
                </h2>
            </header>
        <?php endif; ?>
        
        <?php if ($content): ?>
            <div class="<?php echo lindenhof_bem_classes('omgeving-block', 'content'); ?>">
                <?php echo lindenhof_safe_text($content, true); ?>
            </div>
        <?php endif; ?>
        
        <?php if ($activities && is_array($activities)): ?>
            <div class="<?php echo lindenhof_bem_classes('omgeving-block', 'activities'); ?>">
                
                <h3 class="<?php echo lindenhof_bem_classes('omgeving-block', 'activities-title'); ?>">
                    <?php echo esc_html__('Activiteiten in de omgeving', 'lindenhof'); ?>
                </h3>
                
                <div class="<?php echo lindenhof_bem_classes('omgeving-block', 'activities-list'); ?>">
                    <?php foreach ($activities as $index => $activity): ?>
                        <?php
                        $activity_name = $activity['name'] ?? '';
                        $activity_description = $activity['description'] ?? '';
                        $activity_distance = $activity['distance'] ?? '';
                        $activity_id = lindenhof_unique_id('activity');
                        ?>
                        
                        <article 
                            id="<?php echo esc_attr($activity_id); ?>"
                            class="<?php echo lindenhof_bem_classes('omgeving-block', 'activity'); ?>"
                        >
                            
                            <div class="<?php echo lindenhof_bem_classes('omgeving-block', 'activity-content'); ?>">
                                
                                <?php if ($activity_name): ?>
                                    <h4 class="<?php echo lindenhof_bem_classes('omgeving-block', 'activity-name'); ?>">
                                        <?php echo lindenhof_safe_text($activity_name); ?>
                                    </h4>
                                <?php endif; ?>
                                
                                <div class="<?php echo lindenhof_bem_classes('omgeving-block', 'activity-details'); ?>">
                                    
                                    <?php if ($activity_description): ?>
                                        <p class="<?php echo lindenhof_bem_classes('omgeving-block', 'activity-description'); ?>">
                                            <?php echo lindenhof_safe_text($activity_description); ?>
                                        </p>
                                    <?php endif; ?>
                                    
                                    <?php if ($activity_distance): ?>
                                        <p class="<?php echo lindenhof_bem_classes('omgeving-block', 'activity-distance'); ?>">
                                            <span class="<?php echo lindenhof_bem_classes('omgeving-block', 'distance-label'); ?>">
                                                <?php echo esc_html__('Afstand:', 'lindenhof'); ?>
                                            </span>
                                            <span class="<?php echo lindenhof_bem_classes('omgeving-block', 'distance-value'); ?>">
                                                <?php echo lindenhof_safe_text($activity_distance); ?>
                                            </span>
                                        </p>
                                    <?php endif; ?>
                                    
                                </div>
                                
                            </div>
                            
                        </article>
                        
                    <?php endforeach; ?>
                </div>
                
            </div>
        <?php endif; ?>
        
    </div>
</section>
