<?php
/**
 * Intro Block Template
 * 
 * @package Lindenhof
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get ACF fields
$title = get_field('title');
$subtitle = get_field('subtitle');
$content = get_field('content');
$background_image = get_field('background_image');

// Generate unique ID for this block instance
$block_id = $block_id ?? lindenhof_unique_id('intro-block');

// Build CSS classes
$css_classes = lindenhof_bem_classes('intro-block', '', '', array('lindenhof-block'));

// Build inline styles for background image
$inline_styles = '';
if ($background_image && !empty($background_image['url'])) {
    $inline_styles = sprintf(
        'style="background-image: url(%s);"',
        esc_url($background_image['url'])
    );
}
?>

<section 
    id="<?php echo esc_attr($block_id); ?>" 
    class="<?php echo esc_attr($css_classes); ?>"
    <?php echo $inline_styles; ?>
    <?php echo lindenhof_swup_attributes(); ?>
>
    <div class="<?php echo lindenhof_bem_classes('intro-block', 'container'); ?>">
        
        <?php if ($title): ?>
            <header class="<?php echo lindenhof_bem_classes('intro-block', 'header'); ?>">
                <h1 class="<?php echo lindenhof_bem_classes('intro-block', 'title'); ?>">
                    <?php echo lindenhof_safe_text($title); ?>
                </h1>
                
                <?php if ($subtitle): ?>
                    <p class="<?php echo lindenhof_bem_classes('intro-block', 'subtitle'); ?>">
                        <?php echo lindenhof_safe_text($subtitle); ?>
                    </p>
                <?php endif; ?>
            </header>
        <?php endif; ?>
        
        <?php if ($content): ?>
            <div class="<?php echo lindenhof_bem_classes('intro-block', 'content'); ?>">
                <?php echo lindenhof_safe_text($content, true); ?>
            </div>
        <?php endif; ?>
        
    </div>
    
    <?php if ($background_image): ?>
        <div class="<?php echo lindenhof_bem_classes('intro-block', 'background'); ?>" aria-hidden="true">
            <?php echo lindenhof_safe_image($background_image, 'full', lindenhof_bem_classes('intro-block', 'background-image')); ?>
        </div>
    <?php endif; ?>
    
</section>
